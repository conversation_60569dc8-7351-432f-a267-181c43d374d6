#!/usr/bin/env python3
"""
基于种群状态的自适应多样性管理系统
包含全局解库、多样性监控、自适应注入策略等功能
"""

import torch
import numpy as np
from typing import Optional, List, Dict, Tuple
from dataclasses import dataclass
from collections import deque
import logging

@dataclass
class SolutionRecord:
    """解记录结构"""
    solution: torch.Tensor      # 解向量
    fitness: float             # 适应度值
    generation: int            # 发现代数
    diversity_score: float     # 多样性得分
    source: str               # 来源标识 ('initial', 'evolution', 'injection', 'mutation')

class GlobalSolutionArchive:
    """全局解库 - 维护历史上发现的优质且多样化的解"""
    
    def __init__(self, max_size: int = 1000, diversity_threshold: float = 0.1):
        self.max_size = max_size
        self.diversity_threshold = diversity_threshold
        self.archive: List[SolutionRecord] = []
        self.fitness_history = deque(maxlen=100)  # 用于动态调整阈值
        
    def add_solution(self, solution: torch.Tensor, fitness: float, 
                    generation: int, source: str = 'evolution') -> bool:
        """添加解到全局库"""
        # 计算与现有解的多样性
        diversity_score = self._calculate_diversity_score(solution)
        
        # 创建解记录
        record = SolutionRecord(
            solution=solution.clone(),
            fitness=fitness,
            generation=generation,
            diversity_score=diversity_score,
            source=source
        )
        
        # 判断是否应该添加
        if self._should_add_solution(record):
            self.archive.append(record)
            self.fitness_history.append(fitness)
            
            # 维护库大小
            if len(self.archive) > self.max_size:
                self._remove_redundant_solution()
            
            return True
        return False
    
    def _calculate_diversity_score(self, solution: torch.Tensor) -> float:
        """计算解的多样性得分（与现有解的最小距离）"""
        if not self.archive:
            return float('inf')
        
        min_distance = float('inf')
        for record in self.archive:
            # 使用汉明距离（适用于离散变量）
            distance = torch.sum(solution != record.solution).float() / len(solution)
            min_distance = min(min_distance, distance.item())
        
        return min_distance
    
    def _should_add_solution(self, record: SolutionRecord) -> bool:
        """判断是否应该添加解"""
        # 如果库为空，直接添加
        if not self.archive:
            return True
        
        # 如果多样性足够高，添加
        if record.diversity_score >= self.diversity_threshold:
            return True
        
        # 如果适应度显著更好，也添加
        best_fitness = min(r.fitness for r in self.archive)
        if record.fitness < best_fitness * 0.95:  # 5%的改进阈值
            return True
        
        return False
    
    def _remove_redundant_solution(self):
        """移除冗余解（多样性低且适应度差的解）"""
        if len(self.archive) <= self.max_size:
            return
        
        # 计算每个解的综合得分（适应度 + 多样性）
        scores = []
        for i, record in enumerate(self.archive):
            # 标准化适应度（越小越好）
            fitness_score = record.fitness
            # 多样性得分（越大越好）
            diversity_score = record.diversity_score
            # 综合得分（越小越好）
            combined_score = fitness_score - diversity_score * 0.1
            scores.append((combined_score, i))
        
        # 移除得分最差的解
        scores.sort()
        worst_idx = scores[-1][1]
        del self.archive[worst_idx]
    
    def get_diverse_solutions(self, n: int, current_population: torch.Tensor) -> List[torch.Tensor]:
        """获取与当前种群最不相似的n个解"""
        if not self.archive or n <= 0:
            return []

        # 计算每个存档解与当前种群的最小距离
        candidate_scores = []
        for record in self.archive:
            min_dist_to_pop = float('inf')
            for individual in current_population:
                dist = torch.sum(record.solution != individual).float() / len(record.solution)
                min_dist_to_pop = min(min_dist_to_pop, dist.item())

            candidate_scores.append((min_dist_to_pop, record.solution.clone()))

        # 按距离排序，选择最远的n个
        candidate_scores.sort(key=lambda x: x[0], reverse=True)
        return [solution for _, solution in candidate_scores[:n]]

class PopulationDiversityMonitor:
    """种群多样性监控器"""
    
    def __init__(self, diversity_window: int = 5):
        self.diversity_window = diversity_window
        self.diversity_history = deque(maxlen=diversity_window)
        
    def calculate_population_diversity(self, population: torch.Tensor) -> float:
        """计算种群多样性（平均汉明距离）"""
        if len(population) < 2:
            return 0.0
        
        total_distance = 0.0
        count = 0
        
        for i in range(len(population)):
            for j in range(i + 1, len(population)):
                distance = torch.sum(population[i] != population[j]).float() / len(population[i])
                total_distance += distance.item()
                count += 1
        
        diversity = total_distance / count if count > 0 else 0.0
        self.diversity_history.append(diversity)
        return diversity
    
    def is_diversity_declining(self, threshold: float = 0.05) -> bool:
        """判断多样性是否在下降"""
        if len(self.diversity_history) < self.diversity_window:
            return False
        
        # 计算多样性变化趋势
        recent_avg = np.mean(list(self.diversity_history)[-3:])
        early_avg = np.mean(list(self.diversity_history)[:3])
        
        return (early_avg - recent_avg) > threshold
    
    def get_diversity_status(self) -> Dict[str, float]:
        """获取多样性状态信息"""
        if not self.diversity_history:
            return {"current": 0.0, "trend": 0.0, "status": "unknown"}
        
        current = self.diversity_history[-1]
        if len(self.diversity_history) >= 2:
            trend = self.diversity_history[-1] - self.diversity_history[-2]
        else:
            trend = 0.0
        
        # 判断状态
        if current < 0.1:
            status = "low"
        elif current > 0.3:
            status = "high"
        else:
            status = "medium"
        
        return {
            "current": current,
            "trend": trend,
            "status": status,
            "declining": self.is_diversity_declining()
        }

class AdaptiveDiversityManager:
    """自适应多样性管理器 - 整合全局解库和多样性监控"""
    
    def __init__(self, archive_size: int = 1000, diversity_threshold: float = 0.1):
        self.global_archive = GlobalSolutionArchive(archive_size, diversity_threshold)
        self.diversity_monitor = PopulationDiversityMonitor()
        self.injection_history = deque(maxlen=20)
        
    def update_with_population(self, population: torch.Tensor, fitness_values: torch.Tensor, 
                             generation: int) -> Dict[str, any]:
        """用当前种群更新管理器状态"""
        # 更新全局解库
        for i, (solution, fitness) in enumerate(zip(population, fitness_values)):
            self.global_archive.add_solution(solution, fitness.item(), generation)
        
        # 计算种群多样性
        diversity = self.diversity_monitor.calculate_population_diversity(population)
        diversity_status = self.diversity_monitor.get_diversity_status()
        
        return {
            "diversity": diversity,
            "diversity_status": diversity_status,
            "archive_size": len(self.global_archive.archive)
        }
    
    def should_inject_diversity(self, diversity_status: Dict[str, any]) -> bool:
        """判断是否需要注入多样性"""
        # 多样性过低
        if diversity_status["status"] == "low":
            return True
        
        # 多样性持续下降
        if diversity_status["declining"]:
            return True
        
        # 最近没有注入过
        if not self.injection_history or (len(self.injection_history) > 0 and 
                                        self.injection_history[-1] > 5):
            return diversity_status["current"] < 0.2
        
        return False
    
    def inject_diverse_solutions(self, current_population: torch.Tensor, 
                               injection_ratio: float = 0.2) -> torch.Tensor:
        """注入多样化解到当前种群"""
        n_inject = max(1, int(len(current_population) * injection_ratio))
        
        # 从全局解库获取多样化解
        diverse_solutions = self.global_archive.get_diverse_solutions(n_inject, current_population)
        
        if not diverse_solutions:
            logging.warning("No diverse solutions available for injection")
            return current_population
        
        # 替换种群中最相似的个体
        updated_population = current_population.clone()
        
        for new_solution in diverse_solutions[:n_inject]:
            # 找到与新解最相似的个体进行替换
            similarities = []
            for i, individual in enumerate(updated_population):
                similarity = torch.sum(individual == new_solution).float() / len(individual)
                similarities.append((similarity.item(), i))
            
            # 替换最相似的个体
            similarities.sort(reverse=True)
            replace_idx = similarities[0][1]
            updated_population[replace_idx] = new_solution
        
        # 记录注入历史
        self.injection_history.append(0)  # 重置计数器

        return updated_population

    def update_injection_counter(self):
        """更新注入计数器"""
        if self.injection_history:
            self.injection_history[-1] += 1
        else:
            self.injection_history.append(1)

class DiversityAwareSelection:
    """多样性感知的环境选择策略"""

    def __init__(self, diversity_weight: float = 0.3):
        self.diversity_weight = diversity_weight

    def select_with_diversity(self, population: torch.Tensor, fitness_values: torch.Tensor,
                            selection_size: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """基于适应度和多样性的选择"""
        if len(population) <= selection_size:
            return population, fitness_values

        # 计算适应度排名（越小越好，所以排名越小越好）
        fitness_ranks = torch.argsort(fitness_values)

        # 计算多样性得分
        diversity_scores = self._calculate_diversity_scores(population)

        # 综合得分：适应度权重 + 多样性权重
        combined_scores = []
        for i in range(len(population)):
            fitness_rank = (fitness_ranks == i).nonzero(as_tuple=True)[0].item()
            fitness_score = 1.0 - (fitness_rank / len(population))  # 标准化到[0,1]
            diversity_score = diversity_scores[i]

            combined_score = (1 - self.diversity_weight) * fitness_score + \
                           self.diversity_weight * diversity_score
            combined_scores.append((combined_score, i))

        # 选择得分最高的个体
        combined_scores.sort(reverse=True)
        selected_indices = [idx for _, idx in combined_scores[:selection_size]]

        return population[selected_indices], fitness_values[selected_indices]

    def _calculate_diversity_scores(self, population: torch.Tensor) -> List[float]:
        """计算每个个体的多样性得分"""
        diversity_scores = []

        for i in range(len(population)):
            # 计算与其他个体的平均距离
            distances = []
            for j in range(len(population)):
                if i != j:
                    distance = torch.sum(population[i] != population[j]).float() / len(population[i])
                    distances.append(distance.item())

            avg_distance = np.mean(distances) if distances else 0.0
            diversity_scores.append(avg_distance)

        return diversity_scores

def crowding_distance_selection(population: torch.Tensor, fitness_values: torch.Tensor,
                               selection_size: int) -> Tuple[torch.Tensor, torch.Tensor]:
    """基于拥挤距离的选择（类似NSGA-II）"""
    if len(population) <= selection_size:
        return population, fitness_values

    # 计算拥挤距离
    crowding_distances = torch.zeros(len(population))

    # 按适应度排序
    sorted_indices = torch.argsort(fitness_values)

    # 边界个体设置为无穷大
    crowding_distances[sorted_indices[0]] = float('inf')
    crowding_distances[sorted_indices[-1]] = float('inf')

    # 计算中间个体的拥挤距离
    fitness_range = fitness_values.max() - fitness_values.min()
    if fitness_range > 0:
        for i in range(1, len(sorted_indices) - 1):
            idx = sorted_indices[i]
            prev_idx = sorted_indices[i-1]
            next_idx = sorted_indices[i+1]

            crowding_distances[idx] = (fitness_values[next_idx] - fitness_values[prev_idx]) / fitness_range

    # 选择拥挤距离最大的个体
    selected_indices = torch.argsort(crowding_distances, descending=True)[:selection_size]

    return population[selected_indices], fitness_values[selected_indices]
