#!/usr/bin/env python3
"""
增强版遗传算法 - 完全兼容原始接口
可以直接替换 bounce_ga/candidates_ga.py 中的 run_genetic_algorithm_discrete 函数
"""

import torch
import numpy as np
import gin
from typing import Optional, List
from botorch.acquisition import ExpectedImprovement
from botorch.models import SingleTaskGP
from botorch.sampling import SobolQMCNormalSampler

# 导入原始模块中的必要组件
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adaptive_diversity_management import AdaptiveDiversityManager
from enhanced_discrete_ga import run_enhanced_genetic_algorithm_discrete

@gin.configurable
def run_genetic_algorithm_discrete_enhanced(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus,  # AxUS类型
    trust_region,  # TrustRegion类型
    device: str,
    batch_size: int = 1,
    x_bests: Optional[list[torch.Tensor]] = None,
    population_size: int = 200,
    n_generations: int = 50,
    crossover_rate: float = 0.8,
    mutation_rate: float = 0.2,
    # 原有混合策略参数
    use_hybrid_initialization: bool = True,
    large_sample_ratio: float = 0.3,
    periodic_injection: bool = True,
    injection_interval: int = 10,
    injection_ratio: float = 0.2,
    sampler: Optional[SobolQMCNormalSampler] = None,
    # 新增自适应多样性管理参数
    enable_adaptive_diversity: bool = True,  # 是否启用自适应多样性管理
    archive_size: int = 500,  # 全局解库大小
    diversity_threshold: float = 0.1,  # 多样性阈值
    diversity_injection_ratio: float = 0.2,  # 多样性注入比例
    diversity_selection_weight: float = 0.3,  # 多样性选择权重
    elite_ratio: float = 0.1,  # 精英比例
    seed: Optional[int] = None,
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    增强版离散遗传算法，完全兼容原始接口
    
    新增功能：
    1. 自适应多样性管理
    2. 全局解库维护
    3. 多样性感知选择
    4. 智能多样性注入
    
    Args:
        x_scaled: 当前信赖域样本点 (n, d)
        fx_scaled: 当前样本点函数值 (n,)
        acquisition_function: 用于 batch_size=1 的 EI 函数
        model: 拟合的 GP 代理模型
        axus: AxUS 嵌入对象
        trust_region: 信赖域对象
        device: 计算设备
        batch_size: 批次大小
        x_bests: 外部传入的最好点（用于混合变量）
        population_size: 种群大小
        n_generations: 进化代数
        crossover_rate: 交叉率
        mutation_rate: 变异率
        use_hybrid_initialization: 是否使用混合初始化
        large_sample_ratio: 大规模采样比例
        periodic_injection: 是否周期性注入
        injection_interval: 注入间隔
        injection_ratio: 注入比例
        sampler: 采样器
        enable_adaptive_diversity: 是否启用自适应多样性管理
        archive_size: 全局解库大小
        diversity_threshold: 多样性阈值
        diversity_injection_ratio: 多样性注入比例
        diversity_selection_weight: 多样性选择权重
        elite_ratio: 精英比例
        seed: 随机种子
        
    Returns:
        tuple: (最优解, 最优值, 算法状态信息)
    """
    
    if enable_adaptive_diversity:
        # 使用增强版遗传算法
        return run_enhanced_genetic_algorithm_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=acquisition_function,
            axus=axus,
            trust_region=trust_region,
            x_center=None,  # 在增强版中会自动处理
            device=device,
            population_size=population_size,
            generations=n_generations,
            mutation_rate=mutation_rate,
            crossover_rate=crossover_rate,
            enable_adaptive_diversity=True,
            archive_size=archive_size,
            diversity_threshold=diversity_threshold,
            diversity_injection_ratio=diversity_injection_ratio,
            diversity_selection_weight=diversity_selection_weight,
            use_hybrid_initialization=use_hybrid_initialization,
            large_sample_ratio=large_sample_ratio,
            periodic_injection=periodic_injection,
            injection_interval=injection_interval,
            injection_ratio=injection_ratio,
            elite_ratio=elite_ratio,
            seed=seed
        )
    else:
        # 回退到原始实现
        from bounce_ga.candidates_ga import run_genetic_algorithm_discrete as original_ga
        
        # 调用原始函数，只传递它支持的参数
        return original_ga(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=acquisition_function,
            model=model,
            axus=axus,
            trust_region=trust_region,
            device=device,
            batch_size=batch_size,
            x_bests=x_bests,
            population_size=population_size,
            n_generations=n_generations,
            crossover_rate=crossover_rate,
            mutation_rate=mutation_rate,
            use_hybrid_initialization=use_hybrid_initialization,
            large_sample_ratio=large_sample_ratio,
            periodic_injection=periodic_injection,
            injection_interval=injection_interval,
            injection_ratio=injection_ratio,
            sampler=sampler
        )

def create_backward_compatible_wrapper():
    """
    创建向后兼容的包装器，可以直接替换原始函数
    """
    
    def run_genetic_algorithm_discrete(
        x_scaled: torch.Tensor,
        fx_scaled: torch.Tensor,
        acquisition_function: Optional[ExpectedImprovement],
        model: SingleTaskGP,
        axus,
        trust_region,
        device: str,
        batch_size: int = 1,
        x_bests: Optional[list[torch.Tensor]] = None,
        population_size: int = 200,
        n_generations: int = 50,
        crossover_rate: float = 0.8,
        mutation_rate: float = 0.2,
        use_hybrid_initialization: bool = True,
        large_sample_ratio: float = 0.3,
        periodic_injection: bool = True,
        injection_interval: int = 10,
        injection_ratio: float = 0.2,
        sampler: Optional[SobolQMCNormalSampler] = None,
        # 新增参数，默认启用增强功能
        enable_adaptive_diversity: bool = True,
        **kwargs
    ) -> tuple[torch.Tensor, torch.Tensor, dict]:
        """
        向后兼容的包装器函数
        """
        return run_genetic_algorithm_discrete_enhanced(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=acquisition_function,
            model=model,
            axus=axus,
            trust_region=trust_region,
            device=device,
            batch_size=batch_size,
            x_bests=x_bests,
            population_size=population_size,
            n_generations=n_generations,
            crossover_rate=crossover_rate,
            mutation_rate=mutation_rate,
            use_hybrid_initialization=use_hybrid_initialization,
            large_sample_ratio=large_sample_ratio,
            periodic_injection=periodic_injection,
            injection_interval=injection_interval,
            injection_ratio=injection_ratio,
            sampler=sampler,
            enable_adaptive_diversity=enable_adaptive_diversity,
            **kwargs
        )
    
    return run_genetic_algorithm_discrete

# 创建可直接替换的函数
run_genetic_algorithm_discrete = create_backward_compatible_wrapper()

if __name__ == "__main__":
    print("增强版遗传算法兼容接口已创建")
    print("使用方法：")
    print("1. 直接替换：将此文件中的 run_genetic_algorithm_discrete 函数复制到原文件")
    print("2. 导入替换：from enhanced_ga_compatible import run_genetic_algorithm_discrete")
    print("3. 模块级替换：修改 bounce_ga.py 中的导入语句")
