#!/usr/bin/env python3
"""
集成自适应多样性管理的增强离散遗传算法
"""

import torch
import numpy as np
from typing import Optional, Dict, Tuple
import logging
from adaptive_diversity_management import (
    AdaptiveDiversityManager, 
    DiversityAwareSelection,
    crowding_distance_selection
)

def run_enhanced_genetic_algorithm_discrete(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function,
    axus,
    trust_region,
    x_center: torch.Tensor,
    device: str,
    population_size: int = 100,
    generations: int = 50,
    mutation_rate: float = 0.1,
    crossover_rate: float = 0.8,
    elite_ratio: float = 0.1,
    
    # 自适应多样性管理参数
    enable_adaptive_diversity: bool = True,
    archive_size: int = 500,
    diversity_threshold: float = 0.1,
    diversity_injection_ratio: float = 0.2,
    diversity_selection_weight: float = 0.3,
    
    # 混合策略参数
    use_hybrid_initialization: bool = True,
    large_sample_ratio: float = 0.3,
    periodic_injection: bool = True,
    injection_interval: int = 10,
    injection_ratio: float = 0.2,
    
    x_bests: Optional[list[torch.Tensor]] = None,
    seed: Optional[int] = None,
) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor, dict]:
    """
    增强的离散遗传算法，集成自适应多样性管理
    
    Returns:
        x_best: 最优解
        fx_best: 最优解的采集函数值
        algorithm_state: 算法状态信息
    """
    
    if seed is not None:
        torch.manual_seed(seed)
        np.random.seed(seed)
    
    # 初始化自适应多样性管理器
    diversity_manager = None
    diversity_selector = None
    if enable_adaptive_diversity:
        diversity_manager = AdaptiveDiversityManager(archive_size, diversity_threshold)
        diversity_selector = DiversityAwareSelection(diversity_selection_weight)
    
    # 初始化种群
    if use_hybrid_initialization:
        from bounce_ga.candidates_ga import initialize_population_hybrid
        population = initialize_population_hybrid(
            axus=axus,
            x_center=x_center,
            trust_region=trust_region,
            population_size=population_size,
            device=device,
            large_sample_ratio=large_sample_ratio,
            acquisition_function=acquisition_function,
            x_bests=x_bests
        )
    else:
        from bounce_ga.candidates_ga import initialize_population
        population = initialize_population(
            axus=axus,
            x_center=x_center,
            trust_region=trust_region,
            population_size=population_size,
            device=device,
            x_bests=x_bests
        )
    
    # 评估初始种群
    with torch.no_grad():
        fitness_values = -acquisition_function(population.unsqueeze(1)).squeeze()
    
    # 算法状态跟踪
    algorithm_state = {
        "diversity_history": [],
        "injection_history": [],
        "best_fitness_history": [],
        "archive_size_history": []
    }
    
    # 主进化循环
    for generation in range(generations):
        # 更新多样性管理器
        diversity_info = {}
        if diversity_manager:
            diversity_info = diversity_manager.update_with_population(
                population, fitness_values, generation
            )
            
            # 记录状态
            algorithm_state["diversity_history"].append(diversity_info["diversity"])
            algorithm_state["archive_size_history"].append(diversity_info["archive_size"])
        
        # 检查是否需要多样性注入
        diversity_injected = False
        if (diversity_manager and 
            diversity_manager.should_inject_diversity(diversity_info.get("diversity_status", {}))):
            
            logging.info(f"Generation {generation}: Injecting diversity")
            population = diversity_manager.inject_diverse_solutions(
                population, diversity_injection_ratio
            )
            
            # 重新评估注入后的种群
            with torch.no_grad():
                fitness_values = -acquisition_function(population.unsqueeze(1)).squeeze()
            
            diversity_injected = True
            algorithm_state["injection_history"].append(generation)
        
        # 周期性注入（原有策略）
        if (periodic_injection and not diversity_injected and 
            generation > 0 and generation % injection_interval == 0):
            
            from bounce_ga.candidates_ga import inject_new_individuals
            population = inject_new_individuals(
                current_population=population,
                x_center=x_center,
                axus=axus,
                trust_region=trust_region,
                injection_ratio=injection_ratio,
                acquisition_function=acquisition_function,
                device=device
            )
            
            # 重新评估
            with torch.no_grad():
                fitness_values = -acquisition_function(population.unsqueeze(1)).squeeze()
        
        # 选择操作
        n_elite = max(1, int(population_size * elite_ratio))
        
        if diversity_selector:
            # 使用多样性感知选择
            elite_pop, elite_fitness = diversity_selector.select_with_diversity(
                population, fitness_values, n_elite
            )
        else:
            # 传统精英选择
            elite_indices = torch.topk(fitness_values, n_elite, largest=False)[1]
            elite_pop = population[elite_indices]
            elite_fitness = fitness_values[elite_indices]
        
        # 生成子代
        offspring = generate_offspring(
            elite_pop, population_size - n_elite, 
            crossover_rate, mutation_rate, axus, trust_region, device
        )
        
        # 评估子代
        with torch.no_grad():
            offspring_fitness = -acquisition_function(offspring.unsqueeze(1)).squeeze()
        
        # 合并父代和子代
        combined_pop = torch.cat([elite_pop, offspring], dim=0)
        combined_fitness = torch.cat([elite_fitness, offspring_fitness], dim=0)
        
        # 环境选择
        if diversity_selector and len(combined_pop) > population_size:
            # 使用多样性感知选择
            population, fitness_values = diversity_selector.select_with_diversity(
                combined_pop, combined_fitness, population_size
            )
        else:
            # 使用拥挤距离选择
            population, fitness_values = crowding_distance_selection(
                combined_pop, combined_fitness, population_size
            )
        
        # 更新注入计数器
        if diversity_manager:
            diversity_manager.update_injection_counter()
        
        # 记录最佳适应度
        best_fitness = fitness_values.min().item()
        algorithm_state["best_fitness_history"].append(best_fitness)
        
        # 早停检查（可选）
        if generation > 20 and len(set(algorithm_state["best_fitness_history"][-10:])) == 1:
            logging.info(f"Early stopping at generation {generation} due to convergence")
            break
    
    # 返回最优解
    best_idx = torch.argmin(fitness_values)
    x_best = population[best_idx]
    fx_best = fitness_values[best_idx]
    
    return x_best, fx_best, algorithm_state

def generate_offspring(parent_pop: torch.Tensor, offspring_size: int,
                      crossover_rate: float, mutation_rate: float,
                      axus, trust_region, device: str) -> torch.Tensor:
    """生成子代个体"""
    offspring = []
    
    for _ in range(offspring_size):
        # 选择两个父代
        parent_indices = torch.randperm(len(parent_pop))[:2]
        parent1 = parent_pop[parent_indices[0]]
        parent2 = parent_pop[parent_indices[1]]
        
        # 交叉操作
        if torch.rand(1).item() < crossover_rate:
            child = uniform_crossover(parent1, parent2)
        else:
            child = parent1.clone()
        
        # 变异操作
        if torch.rand(1).item() < mutation_rate:
            child = discrete_mutation(child, axus, trust_region)
        
        offspring.append(child)
    
    return torch.stack(offspring)

def uniform_crossover(parent1: torch.Tensor, parent2: torch.Tensor) -> torch.Tensor:
    """均匀交叉"""
    mask = torch.rand(len(parent1)) < 0.5
    child = parent1.clone()
    child[mask] = parent2[mask]
    return child

def discrete_mutation(individual: torch.Tensor, axus, trust_region) -> torch.Tensor:
    """离散变异"""
    mutated = individual.clone()
    
    # 随机选择一个位置进行变异
    mutation_pos = torch.randint(0, len(individual), (1,)).item()
    
    # 在信赖域范围内随机变异
    mutated[mutation_pos] = torch.rand(1, device=individual.device).item()
    
    # 确保在[0,1]范围内
    mutated = torch.clamp(mutated, 0, 1)
    
    return mutated
