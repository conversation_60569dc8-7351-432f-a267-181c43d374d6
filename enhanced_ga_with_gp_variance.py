#!/usr/bin/env python3
"""
增强版遗传算法 - 集成GP方差和随机解注入
结合用户建议：
1. 环境选择时加入GP方差最大的解
2. 考虑方差和目标值的平衡
3. 每次迭代注入TR区域内的随机解
"""

import torch
import numpy as np
import gin
from typing import Tuple, Dict, Any, Optional
import logging

@gin.configurable
def run_genetic_algorithm_discrete(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function,
    axus,
    trust_region,
    x_center: torch.Tensor = None,
    device: str = "cpu",
    population_size: int = 50,
    generations: int = 20,
    batch_size: int = 1,
    
    # GP方差和随机解注入参数
    enable_gp_variance_injection: bool = True,
    gp_variance_ratio: float = 0.1,  # 注入GP方差最大解的比例
    enable_random_injection: bool = True,
    random_injection_ratio: float = 0.05,  # 注入随机解的比例
    variance_objective_weight: float = 0.3,  # 方差-目标值权重平衡
    
    # 基本GA参数
    crossover_rate: float = 0.8,
    mutation_rate: float = 0.1,
    elite_ratio: float = 0.2,
    
    # 简化的多样性参数
    enable_diversity: bool = True,
    diversity_check_interval: int = 10,
    diversity_threshold: float = 0.1,
    max_injection_size: int = 2,
) -> Tuple[torch.Tensor, torch.Tensor, Dict[str, Any]]:
    """
    增强版遗传算法，集成GP方差和随机解注入
    """
    
    # 处理x_center
    if x_center is None:
        if len(x_scaled) > 0:
            best_idx = torch.argmin(fx_scaled)
            x_center = x_scaled[best_idx]
        else:
            x_center = torch.rand(axus.target_dim, device=device)
    
    # 初始化种群
    population = initialize_population_enhanced(
        population_size, axus, trust_region, x_center, device,
        enable_random_injection, random_injection_ratio
    )
    
    # 评估初始种群
    with torch.no_grad():
        fitness_values = -acquisition_function(population.unsqueeze(1)).squeeze()
    
    # 简化的解库
    solution_archive = []
    max_archive_size = 20
    
    # 算法状态
    algorithm_state = {
        "diversity_history": [],
        "injection_history": [],
        "best_fitness_history": [],
        "archive_size_history": [],
        "gp_variance_history": [],
        "random_injection_history": []
    }
    
    # 主进化循环
    for generation in range(generations):
        # 记录最佳适应度
        best_fitness = fitness_values.min().item()
        algorithm_state["best_fitness_history"].append(best_fitness)
        
        # 标准遗传算法操作
        # 选择
        n_elite = max(1, int(population_size * elite_ratio))
        elite_indices = torch.topk(fitness_values, n_elite, largest=False)[1]
        elite_pop = population[elite_indices]
        elite_fitness = fitness_values[elite_indices]

        # 生成子代（生成更多子代以确保环境选择被触发）
        n_offspring = population_size  # 生成与种群大小相等的子代
        offspring = generate_offspring_fast(
            elite_pop, n_offspring,
            crossover_rate, mutation_rate, axus, trust_region, device
        )
        
        # 评估子代
        with torch.no_grad():
            offspring_fitness = -acquisition_function(offspring.unsqueeze(1)).squeeze()
        
        # 合并种群
        combined_pop = torch.cat([elite_pop, offspring], dim=0)
        combined_fitness = torch.cat([elite_fitness, offspring_fitness], dim=0)
        
        # 增强的环境选择：集成GP方差最大解
        if len(combined_pop) > population_size:  # 只有当需要选择时才调用
            population, fitness_values = enhanced_environmental_selection(
                combined_pop, combined_fitness, population_size,
                acquisition_function, axus, trust_region, x_center,
                enable_gp_variance_injection, gp_variance_ratio, variance_objective_weight,
                algorithm_state, generation, device
            )
        else:
            population, fitness_values = combined_pop, combined_fitness
        
        # 随机解注入
        if enable_random_injection and generation % 5 == 0:  # 每5代注入一次
            population, fitness_values = inject_random_solutions(
                population, fitness_values, axus, trust_region, x_center,
                random_injection_ratio, acquisition_function, device,
                algorithm_state, generation
            )
        
        # 简化的多样性管理
        if enable_diversity and generation % diversity_check_interval == 0:
            manage_diversity_simple(
                population, fitness_values, solution_archive, max_archive_size,
                diversity_threshold, max_injection_size, acquisition_function,
                algorithm_state, generation
            )
    
    # 返回最佳解
    best_idx = torch.argmin(fitness_values)
    x_best = population[best_idx].unsqueeze(0)
    fx_best = fitness_values[best_idx].unsqueeze(0).unsqueeze(0)
    
    # 坐标变换：[0,1] -> [-1,1]
    x_best = x_best * 2 - 1
    
    # 构建信赖域状态
    tr_state = {
        "center": x_center,
        "length": np.array([trust_region.length_discrete]),
        **algorithm_state
    }
    
    return x_best, fx_best, tr_state


def enhanced_environmental_selection(
    combined_pop: torch.Tensor, 
    combined_fitness: torch.Tensor, 
    population_size: int,
    acquisition_function,
    axus, trust_region, x_center: torch.Tensor,
    enable_gp_variance: bool,
    gp_variance_ratio: float,
    variance_objective_weight: float,
    algorithm_state: Dict,
    generation: int,
    device: str
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    增强的环境选择：集成GP方差最大解
    """
    
    if not enable_gp_variance or len(combined_pop) <= population_size:
        # 标准选择
        selected_indices = torch.topk(combined_fitness, population_size, largest=False)[1]
        return combined_pop[selected_indices], combined_fitness[selected_indices]
    
    # 计算GP方差（如果采集函数支持）
    try:
        # 尝试获取GP方差
        gp_variance = get_gp_variance(combined_pop, acquisition_function)

        if gp_variance is not None:
            # 计算方差-目标值组合得分
            # 标准化方差和目标值
            normalized_variance = (gp_variance - gp_variance.min()) / (gp_variance.max() - gp_variance.min() + 1e-8)
            normalized_fitness = (combined_fitness - combined_fitness.min()) / (combined_fitness.max() - combined_fitness.min() + 1e-8)
            
            # 组合得分：低目标值 + 高方差
            combined_score = (1 - variance_objective_weight) * normalized_fitness - variance_objective_weight * normalized_variance
            
            # 选择策略：一部分基于组合得分，一部分基于纯目标值
            n_variance_based = int(population_size * gp_variance_ratio)
            n_fitness_based = population_size - n_variance_based
            
            # 基于组合得分选择
            variance_indices = torch.topk(combined_score, n_variance_based, largest=False)[1]
            
            # 基于纯目标值选择（排除已选择的）
            remaining_mask = torch.ones(len(combined_fitness), dtype=torch.bool)
            remaining_mask[variance_indices] = False
            remaining_fitness = combined_fitness[remaining_mask]
            remaining_indices = torch.arange(len(combined_fitness))[remaining_mask]
            
            if len(remaining_fitness) >= n_fitness_based:
                fitness_selected = torch.topk(remaining_fitness, n_fitness_based, largest=False)[1]
                fitness_indices = remaining_indices[fitness_selected]
            else:
                fitness_indices = remaining_indices
            
            # 合并选择的索引
            selected_indices = torch.cat([variance_indices, fitness_indices])
            
            # 记录GP方差信息
            algorithm_state["gp_variance_history"].append({
                "generation": generation,
                "mean_variance": gp_variance.mean().item(),
                "max_variance": gp_variance.max().item(),
                "variance_based_selections": n_variance_based
            })
            
            return combined_pop[selected_indices], combined_fitness[selected_indices]
    
    except Exception as e:
        logging.debug(f"GP variance calculation failed: {e}")
    
    # 回退到标准选择
    selected_indices = torch.topk(combined_fitness, population_size, largest=False)[1]
    return combined_pop[selected_indices], combined_fitness[selected_indices]


def get_gp_variance(x: torch.Tensor, acquisition_function) -> Optional[torch.Tensor]:
    """
    尝试从采集函数获取GP方差
    """
    try:
        # 方法1：直接调用get_variance方法
        if hasattr(acquisition_function, 'get_variance'):
            variance = acquisition_function.get_variance(x)
            if variance is not None:
                return variance

        # 方法2：检查采集函数是否有GP模型
        if hasattr(acquisition_function, 'model'):
            model = acquisition_function.model
            if hasattr(model, 'posterior'):
                # 获取后验分布
                with torch.no_grad():
                    # 确保输入格式正确
                    x_input = x.unsqueeze(1) if len(x.shape) == 2 else x
                    posterior = model.posterior(x_input)
                    variance = posterior.variance.squeeze()
                    return variance

        # 方法3：尝试BoTorch风格的采集函数
        if hasattr(acquisition_function, 'model') and hasattr(acquisition_function.model, 'posterior'):
            with torch.no_grad():
                x_input = x.unsqueeze(1) if len(x.shape) == 2 else x
                posterior = acquisition_function.model.posterior(x_input)
                if hasattr(posterior, 'variance'):
                    return posterior.variance.squeeze()
                elif hasattr(posterior, 'distribution') and hasattr(posterior.distribution, 'variance'):
                    return posterior.distribution.variance.squeeze()

        # 方法4：检查是否是我们的测试模拟函数
        if hasattr(acquisition_function, '__class__') and 'Mock' in acquisition_function.__class__.__name__:
            if hasattr(acquisition_function, 'get_variance'):
                return acquisition_function.get_variance(x)

    except Exception as e:
        logging.debug(f"GP variance calculation failed: {e}")

    return None


def inject_random_solutions(
    population: torch.Tensor,
    fitness_values: torch.Tensor,
    axus, trust_region, x_center: torch.Tensor,
    injection_ratio: float,
    acquisition_function,
    device: str,
    algorithm_state: Dict,
    generation: int
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    注入TR区域内的随机解
    """
    n_inject = max(1, int(len(population) * injection_ratio))
    
    # 生成TR区域内的随机解
    random_solutions = sample_tr_random_solutions(
        n_inject, axus, trust_region, x_center, device
    )
    
    # 评估随机解
    with torch.no_grad():
        random_fitness = -acquisition_function(random_solutions.unsqueeze(1)).squeeze()
    
    # 替换最差的解
    worst_indices = torch.topk(fitness_values, n_inject, largest=True)[1]
    
    new_population = population.clone()
    new_fitness = fitness_values.clone()
    
    new_population[worst_indices] = random_solutions
    new_fitness[worst_indices] = random_fitness
    
    # 记录注入信息
    algorithm_state["random_injection_history"].append({
        "generation": generation,
        "n_injected": n_inject,
        "best_injected_fitness": random_fitness.min().item()
    })
    
    return new_population, new_fitness


def sample_tr_random_solutions(
    n_solutions: int,
    axus, trust_region, x_center: torch.Tensor,
    device: str
) -> torch.Tensor:
    """
    在TR区域内采样随机解
    """
    # 转换中心点到[0,1]空间
    center_01 = (x_center + 1) / 2
    center_01 = torch.clamp(center_01, 0, 1)
    
    # 计算TR半径（在[0,1]空间中）
    tr_radius = trust_region.length_discrete / 2.0  # 假设原始空间是[-1,1]
    
    solutions = []
    for _ in range(n_solutions):
        # 在TR区域内随机采样
        random_offset = (torch.rand(axus.target_dim, device=device) - 0.5) * 2 * tr_radius
        solution = center_01 + random_offset
        solution = torch.clamp(solution, 0, 1)
        solutions.append(solution)
    
    return torch.stack(solutions)


# 其他辅助函数保持不变...
def initialize_population_enhanced(population_size: int, axus, trust_region, x_center: torch.Tensor, device: str,
                                 enable_random_injection: bool, random_injection_ratio: float) -> torch.Tensor:
    """增强的种群初始化"""
    population = []
    
    # 添加中心点
    if x_center is not None:
        center_01 = (x_center + 1) / 2
        center_01 = torch.clamp(center_01, 0, 1)
        population.append(center_01)
    
    # 如果启用随机注入，一部分用TR区域采样
    if enable_random_injection:
        n_tr_samples = int(population_size * random_injection_ratio)
        tr_samples = sample_tr_random_solutions(n_tr_samples, axus, trust_region, x_center, device)
        population.extend(tr_samples)
    
    # 生成剩余个体
    remaining = population_size - len(population)
    for _ in range(remaining):
        individual = torch.rand(axus.target_dim, device=device)
        population.append(individual)
    
    return torch.stack(population)


def generate_offspring_fast(parent_pop: torch.Tensor, offspring_size: int,
                           crossover_rate: float, mutation_rate: float,
                           axus, trust_region, device: str) -> torch.Tensor:
    """快速生成子代"""
    offspring = []
    
    for _ in range(offspring_size):
        parent_indices = torch.randperm(len(parent_pop))[:2]
        parent1 = parent_pop[parent_indices[0]]
        parent2 = parent_pop[parent_indices[1]] if len(parent_indices) > 1 else parent1
        
        if torch.rand(1).item() < crossover_rate:
            child = uniform_crossover_fast(parent1, parent2)
        else:
            child = parent1.clone()
        
        if torch.rand(1).item() < mutation_rate:
            child = uniform_mutation_fast(child, device)
        
        offspring.append(child)
    
    return torch.stack(offspring)


def uniform_crossover_fast(parent1: torch.Tensor, parent2: torch.Tensor) -> torch.Tensor:
    """快速均匀交叉"""
    mask = torch.rand(len(parent1)) < 0.5
    child = parent1.clone()
    child[mask] = parent2[mask]
    return child


def uniform_mutation_fast(individual: torch.Tensor, device: str) -> torch.Tensor:
    """快速均匀变异"""
    mutated = individual.clone()
    mutation_pos = torch.randint(0, len(individual), (1,)).item()
    mutated[mutation_pos] = torch.rand(1, device=device).item()
    return mutated


def manage_diversity_simple(population, fitness_values, solution_archive, max_archive_size,
                          diversity_threshold, max_injection_size, acquisition_function,
                          algorithm_state, generation):
    """简化的多样性管理"""
    if len(population) > 1:
        diversity = torch.pdist(population.float()).mean().item()
        algorithm_state["diversity_history"].append(diversity)
        
        # 更新解库
        best_idx = torch.argmin(fitness_values)
        best_solution = population[best_idx].clone()
        best_fit = fitness_values[best_idx].item()
        
        if len(solution_archive) < max_archive_size:
            solution_archive.append((best_solution, best_fit))
        else:
            worst_idx = max(range(len(solution_archive)), key=lambda i: solution_archive[i][1])
            if best_fit < solution_archive[worst_idx][1]:
                solution_archive[worst_idx] = (best_solution, best_fit)
        
        algorithm_state["archive_size_history"].append(len(solution_archive))
