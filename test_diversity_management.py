#!/usr/bin/env python3
"""
测试自适应多样性管理系统的核心功能
"""

import torch
import numpy as np
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

from adaptive_diversity_management import (
    AdaptiveDiversityManager,
    DiversityAwareSelection,
    crowding_distance_selection
)

def test_global_solution_archive():
    """测试全局解库功能"""
    print("=" * 60)
    print("测试全局解库功能")
    print("=" * 60)
    
    from adaptive_diversity_management import GlobalSolutionArchive
    
    # 创建解库
    archive = GlobalSolutionArchive(max_size=10, diversity_threshold=0.1)
    
    # 生成测试解
    solutions = []
    for i in range(15):
        # 创建不同多样性的解
        if i < 5:
            # 高多样性解
            solution = torch.rand(10)
        elif i < 10:
            # 中等多样性解（基于第一个解的变异）
            solution = solutions[0].clone() if solutions else torch.rand(10)
            solution += torch.randn(10) * 0.1
        else:
            # 低多样性解（几乎相同）
            solution = solutions[0].clone() if solutions else torch.rand(10)
            solution += torch.randn(10) * 0.01
        
        solutions.append(solution)
        
        # 添加到解库
        fitness = torch.sum(solution**2).item()  # 简单的适应度函数
        added = archive.add_solution(solution, fitness, i, 'test')
        
        print(f"解 {i+1}: 适应度={fitness:.4f}, 添加={'是' if added else '否'}, 库大小={len(archive.archive)}")
    
    print(f"\n最终解库大小: {len(archive.archive)}")
    print(f"解库容量: {archive.max_size}")
    
    # 测试获取多样化解
    current_pop = torch.stack(solutions[:3])  # 当前种群
    diverse_solutions = archive.get_diverse_solutions(3, current_pop)
    print(f"获取到 {len(diverse_solutions)} 个多样化解")

def test_diversity_monitor():
    """测试种群多样性监控"""
    print("\n" + "=" * 60)
    print("测试种群多样性监控")
    print("=" * 60)
    
    from adaptive_diversity_management import PopulationDiversityMonitor
    
    monitor = PopulationDiversityMonitor()
    
    # 模拟种群进化过程
    print("模拟种群多样性变化:")
    for generation in range(10):
        if generation < 3:
            # 初期：高多样性
            population = torch.rand(20, 10)
        elif generation < 7:
            # 中期：多样性逐渐降低
            base = torch.rand(1, 10)
            noise_scale = 0.5 - generation * 0.05
            population = base + torch.randn(20, 10) * noise_scale
        else:
            # 后期：低多样性（收敛）
            base = torch.rand(1, 10)
            population = base + torch.randn(20, 10) * 0.1
        
        diversity = monitor.calculate_population_diversity(population)
        status = monitor.get_diversity_status()
        
        print(f"第{generation+1}代: 多样性={diversity:.4f}, 状态={status['status']}, "
              f"趋势={'下降' if status['declining'] else '稳定/上升'}")

def test_diversity_aware_selection():
    """测试多样性感知选择"""
    print("\n" + "=" * 60)
    print("测试多样性感知选择")
    print("=" * 60)
    
    selector = DiversityAwareSelection(diversity_weight=0.3)
    
    # 创建测试种群
    population = torch.rand(20, 10)
    fitness_values = torch.sum(population**2, dim=1)  # 简单适应度
    
    print(f"原始种群大小: {len(population)}")
    print(f"适应度范围: [{fitness_values.min():.4f}, {fitness_values.max():.4f}]")
    
    # 计算原始多样性
    original_diversity = calculate_population_diversity(population)
    print(f"原始种群多样性: {original_diversity:.4f}")
    
    # 使用多样性感知选择
    selected_pop, selected_fitness = selector.select_with_diversity(
        population, fitness_values, 10
    )
    
    selected_diversity = calculate_population_diversity(selected_pop)
    print(f"选择后种群大小: {len(selected_pop)}")
    print(f"选择后多样性: {selected_diversity:.4f}")
    print(f"选择后适应度范围: [{selected_fitness.min():.4f}, {selected_fitness.max():.4f}]")
    
    # 对比传统精英选择
    elite_indices = torch.topk(fitness_values, 10, largest=False)[1]
    elite_pop = population[elite_indices]
    elite_diversity = calculate_population_diversity(elite_pop)
    
    print(f"\n对比传统精英选择:")
    print(f"精英选择多样性: {elite_diversity:.4f}")
    print(f"多样性改进: {((selected_diversity - elite_diversity) / elite_diversity * 100):+.1f}%")

def test_adaptive_diversity_manager():
    """测试自适应多样性管理器"""
    print("\n" + "=" * 60)
    print("测试自适应多样性管理器")
    print("=" * 60)
    
    manager = AdaptiveDiversityManager(archive_size=50, diversity_threshold=0.1)
    
    # 模拟进化过程
    print("模拟进化算法运行:")
    for generation in range(15):
        # 生成当前种群
        if generation < 5:
            # 初期：随机种群
            population = torch.rand(20, 10)
        else:
            # 后期：逐渐收敛的种群
            base = torch.tensor([0.5] * 10)
            noise_scale = max(0.1, 0.5 - generation * 0.03)
            population = base + torch.randn(20, 10) * noise_scale
            population = torch.clamp(population, 0, 1)
        
        fitness_values = torch.sum(population**2, dim=1)
        
        # 更新管理器
        diversity_info = manager.update_with_population(population, fitness_values, generation)
        
        # 检查是否需要注入
        should_inject = manager.should_inject_diversity(diversity_info.get("diversity_status", {}))
        
        injected = ""
        if should_inject:
            # 执行多样性注入
            new_population = manager.inject_diverse_solutions(population, 0.2)
            injected = " [注入多样性]"
            population = new_population
        
        manager.update_injection_counter()
        
        print(f"第{generation+1}代: 多样性={diversity_info['diversity']:.4f}, "
              f"解库大小={diversity_info['archive_size']}, "
              f"状态={diversity_info.get('diversity_status', {}).get('status', 'unknown')}"
              f"{injected}")

def calculate_population_diversity(population: torch.Tensor) -> float:
    """计算种群多样性"""
    if len(population) < 2:
        return 0.0
    
    total_distance = 0.0
    count = 0
    
    for i in range(len(population)):
        for j in range(i + 1, len(population)):
            distance = torch.sum(population[i] != population[j]).float() / len(population[i])
            total_distance += distance.item()
            count += 1
    
    return total_distance / count if count > 0 else 0.0

def test_crowding_distance_selection():
    """测试拥挤距离选择"""
    print("\n" + "=" * 60)
    print("测试拥挤距离选择")
    print("=" * 60)
    
    # 创建测试种群
    population = torch.rand(20, 10)
    fitness_values = torch.sum(population**2, dim=1)
    
    print(f"原始种群大小: {len(population)}")
    original_diversity = calculate_population_diversity(population)
    print(f"原始多样性: {original_diversity:.4f}")
    
    # 使用拥挤距离选择
    selected_pop, selected_fitness = crowding_distance_selection(
        population, fitness_values, 10
    )
    
    selected_diversity = calculate_population_diversity(selected_pop)
    print(f"选择后种群大小: {len(selected_pop)}")
    print(f"选择后多样性: {selected_diversity:.4f}")
    print(f"多样性保持率: {(selected_diversity / original_diversity * 100):.1f}%")

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    print("开始测试自适应多样性管理系统...")
    
    # 运行各项测试
    test_global_solution_archive()
    test_diversity_monitor()
    test_diversity_aware_selection()
    test_crowding_distance_selection()
    test_adaptive_diversity_manager()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)
