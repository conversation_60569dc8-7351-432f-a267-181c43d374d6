#!/usr/bin/env python3
"""
测试混合策略离散遗传算法的性能
对比原始GA、混合初始化GA、完整混合策略GA的效果
"""

import torch
import numpy as np
import time
from typing import Optional

# 导入必要的模块
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bounce_ga.candidates_ga import run_genetic_algorithm_discrete
from bounce.projection import AxUS
from bounce.trust_region import TrustRegion
from bounce.util.benchmark import ParameterType, Parameter
from botorch.models import SingleTaskGP
from botorch.acquisition import ExpectedImprovement
from gpytorch.kernels import MaternKernel
from bounce.kernel.categorical_mixture import MixtureKernel


def create_test_problem(n_binary=5, n_categorical=3, categorical_sizes=[3, 4, 5]):
    """创建测试问题"""
    # 创建参数列表
    parameters = []

    # 添加二进制参数
    for i in range(n_binary):
        param = Parameter(
            name=f'b{i}',
            type=ParameterType.BINARY,
            lower_bound=0,
            upper_bound=1
        )
        parameters.append(param)

    # 添加分类参数
    for i, size in enumerate(categorical_sizes[:n_categorical]):
        param = Parameter(
            name=f'c{i}',
            type=ParameterType.CATEGORICAL,
            lower_bound=0,
            upper_bound=size-1
        )
        parameters.append(param)

    # 创建AxUS嵌入
    axus = AxUS(parameters=parameters, n_bins=len(parameters))

    # 创建信赖域
    trust_region = TrustRegion(dimensionality=axus.target_dim)

    return axus, trust_region


def create_mock_gp_model(axus, device="cpu"):
    """创建模拟GP模型"""
    # 创建一些训练数据
    n_train = 20
    x_train = torch.rand(n_train, axus.target_dim, device=device)
    
    # 创建模拟目标函数值（Ackley函数的离散版本）
    y_train = torch.zeros(n_train, 1, device=device)
    for i in range(n_train):
        x_val = x_train[i]
        # 简化的Ackley函数
        a = 20
        b = 0.2
        c = 2 * np.pi
        sum1 = torch.sum(x_val ** 2)
        sum2 = torch.sum(torch.cos(c * x_val))
        y_val = -a * torch.exp(-b * torch.sqrt(sum1 / len(x_val))) - torch.exp(sum2 / len(x_val)) + a + np.e
        y_train[i] = y_val
    
    # 标准化
    y_mean = y_train.mean()
    y_std = y_train.std()
    y_train_scaled = (y_train - y_mean) / y_std
    
    # 获取离散和连续维度索引
    discrete_dims = []
    continuous_dims = []

    for i in range(axus.target_dim):
        # 假设所有维度都是离散的（因为我们只测试离散GA）
        discrete_dims.append(i)

    # 创建GP模型
    if len(continuous_dims) > 0:
        kernel = MixtureKernel(
            discrete_dims=discrete_dims,
            continuous_dims=continuous_dims,
        )
    else:
        # 如果没有连续维度，使用简单的Matern核
        kernel = MaternKernel(nu=2.5)
    
    model = SingleTaskGP(x_train, y_train_scaled, covar_module=kernel)
    model.eval()
    
    # 创建采集函数
    acquisition_function = ExpectedImprovement(model=model, best_f=y_train_scaled.min())
    
    return model, acquisition_function, x_train, y_train_scaled, y_mean, y_std


def test_ga_configurations():
    """测试不同GA配置的性能"""
    device = "cpu"
    axus, trust_region = create_test_problem()
    model, acquisition_function, x_scaled, fx_scaled, y_mean, y_std = create_mock_gp_model(axus, device)
    
    # 测试配置
    configs = [
        {
            "name": "原始GA",
            "use_hybrid_initialization": False,
            "periodic_injection": False,
        },
        {
            "name": "混合初始化GA", 
            "use_hybrid_initialization": True,
            "large_sample_ratio": 0.3,
            "periodic_injection": False,
        },
        {
            "name": "完整混合策略GA",
            "use_hybrid_initialization": True,
            "large_sample_ratio": 0.3,
            "periodic_injection": True,
            "injection_interval": 10,
            "injection_ratio": 0.2,
        }
    ]
    
    results = {}
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        print("=" * 50)
        
        # 运行多次取平均
        n_runs = 3
        times = []
        best_values = []
        
        for run in range(n_runs):
            print(f"运行 {run + 1}/{n_runs}")
            
            start_time = time.time()
            
            try:
                x_best, fx_best, tr_state = run_genetic_algorithm_discrete(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    acquisition_function=acquisition_function,
                    model=model,
                    axus=axus,
                    trust_region=trust_region,
                    device=device,
                    batch_size=1,
                    population_size=50,  # 较小的种群用于快速测试
                    n_generations=20,    # 较少的代数用于快速测试
                    **{k: v for k, v in config.items() if k != "name"}
                )
                
                end_time = time.time()
                runtime = end_time - start_time
                
                # 反标准化
                fx_best_original = fx_best * y_std + y_mean
                
                times.append(runtime)
                best_values.append(fx_best_original.item())
                
                print(f"  运行时间: {runtime:.3f}s")
                print(f"  最优值: {fx_best_original.item():.6f}")
                
            except Exception as e:
                print(f"  错误: {e}")
                continue
        
        if times:
            results[config['name']] = {
                'avg_time': np.mean(times),
                'std_time': np.std(times),
                'avg_best': np.mean(best_values),
                'std_best': np.std(best_values),
                'times': times,
                'best_values': best_values
            }
            
            print(f"\n{config['name']} 总结:")
            print(f"  平均运行时间: {np.mean(times):.3f} ± {np.std(times):.3f}s")
            print(f"  平均最优值: {np.mean(best_values):.6f} ± {np.std(best_values):.6f}")
    
    return results


def print_results_table(results):
    """打印结果对比表格"""
    if not results:
        print("没有结果可以打印")
        return

    print("\n" + "="*80)
    print("混合策略GA性能对比结果")
    print("="*80)
    print(f"{'配置':<20} {'平均时间(s)':<15} {'平均最优值':<15} {'时间改进':<12} {'质量改进':<12}")
    print("-"*80)

    baseline = None
    if "原始GA" in results:
        baseline = results["原始GA"]

    for name, result in results.items():
        time_str = f"{result['avg_time']:.3f}±{result['std_time']:.3f}"
        best_str = f"{result['avg_best']:.4f}±{result['std_best']:.4f}"

        if baseline and name != "原始GA":
            time_improvement = (baseline['avg_time'] - result['avg_time']) / baseline['avg_time'] * 100
            quality_improvement = (baseline['avg_best'] - result['avg_best']) / abs(baseline['avg_best']) * 100
            time_imp_str = f"{time_improvement:+.1f}%"
            quality_imp_str = f"{quality_improvement:+.1f}%"
        else:
            time_imp_str = "基准"
            quality_imp_str = "基准"

        print(f"{name:<20} {time_str:<15} {best_str:<15} {time_imp_str:<12} {quality_imp_str:<12}")

    print("="*80)


if __name__ == "__main__":
    print("开始测试混合策略离散遗传算法...")
    
    # 运行测试
    results = test_ga_configurations()
    
    # 打印结果表格
    print_results_table(results)
    
    # 打印详细结果
    print("\n" + "="*60)
    print("详细结果对比")
    print("="*60)
    
    for name, result in results.items():
        print(f"\n{name}:")
        print(f"  运行时间: {result['avg_time']:.3f} ± {result['std_time']:.3f}s")
        print(f"  最优值: {result['avg_best']:.6f} ± {result['std_best']:.6f}")
        
        # 计算相对于原始GA的改进
        if name != "原始GA" and "原始GA" in results:
            baseline = results["原始GA"]
            time_improvement = (baseline['avg_time'] - result['avg_time']) / baseline['avg_time'] * 100
            quality_improvement = (baseline['avg_best'] - result['avg_best']) / abs(baseline['avg_best']) * 100
            
            print(f"  相对原始GA改进:")
            print(f"    时间: {time_improvement:+.1f}%")
            print(f"    质量: {quality_improvement:+.1f}%")
