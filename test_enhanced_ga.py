#!/usr/bin/env python3
"""
测试增强的自适应多样性管理遗传算法
"""

import torch
import numpy as np
import time
from typing import Dict, List
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 导入必要的模块
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_discrete_ga import run_enhanced_genetic_algorithm_discrete
from bounce_ga.candidates_ga import run_genetic_algorithm_discrete

def create_test_environment():
    """创建测试环境"""
    # 模拟AxUS和TrustRegion对象
    class MockAxUS:
        def __init__(self):
            self.target_dim = 20

    class MockTrustRegion:
        def __init__(self):
            self.length_discrete = 0.5

    class MockModel:
        def __init__(self):
            pass

    # 模拟采集函数
    def mock_acquisition_function(x):
        """模拟采集函数：多峰函数，有多个局部最优"""
        x_np = x.squeeze().cpu().numpy()
        if x_np.ndim == 1:
            x_np = x_np.reshape(1, -1)

        results = []
        for sample in x_np:
            # 多峰函数：Rastrigin + 噪声
            result = 10 * len(sample) + np.sum(sample**2 - 10 * np.cos(2 * np.pi * sample))
            # 添加一些随机性
            result += np.random.normal(0, 0.1)
            results.append(result)

        return torch.tensor(results, dtype=torch.float32)

    axus = MockAxUS()
    trust_region = MockTrustRegion()
    model = MockModel()
    x_center = torch.rand(axus.target_dim)

    # 模拟历史数据
    x_scaled = torch.rand(50, axus.target_dim)
    fx_scaled = mock_acquisition_function(x_scaled)

    return axus, trust_region, model, x_center, x_scaled, fx_scaled, mock_acquisition_function

def run_comparison_test():
    """运行对比测试"""
    print("开始测试增强的自适应多样性管理遗传算法...")
    
    # 创建测试环境
    axus, trust_region, model, x_center, x_scaled, fx_scaled, acquisition_function = create_test_environment()
    
    # 测试配置
    test_configs = [
        {
            "name": "原始GA",
            "use_enhanced": False,
            "params": {
                "population_size": 50,
                "generations": 30,
                "mutation_rate": 0.1,
                "crossover_rate": 0.8,
                "elite_ratio": 0.1,
            }
        },
        {
            "name": "增强GA（基础多样性管理）",
            "use_enhanced": True,
            "params": {
                "population_size": 50,
                "generations": 30,
                "mutation_rate": 0.1,
                "crossover_rate": 0.8,
                "elite_ratio": 0.1,
                "enable_adaptive_diversity": True,
                "use_hybrid_initialization": False,
                "periodic_injection": False,
                "diversity_selection_weight": 0.2,
            }
        },
        {
            "name": "增强GA（完整策略）",
            "use_enhanced": True,
            "params": {
                "population_size": 50,
                "generations": 30,
                "mutation_rate": 0.1,
                "crossover_rate": 0.8,
                "elite_ratio": 0.1,
                "enable_adaptive_diversity": True,
                "use_hybrid_initialization": True,
                "periodic_injection": True,
                "diversity_selection_weight": 0.3,
                "large_sample_ratio": 0.3,
                "injection_interval": 8,
                "injection_ratio": 0.2,
            }
        }
    ]
    
    results = {}
    n_runs = 5
    
    for config in test_configs:
        print(f"\n测试配置: {config['name']}")
        print("=" * 50)
        
        run_times = []
        best_values = []
        diversity_histories = []
        
        for run in range(n_runs):
            print(f"运行 {run + 1}/{n_runs}")
            
            start_time = time.time()
            
            if config["use_enhanced"]:
                x_best, fx_best, algorithm_state = run_enhanced_genetic_algorithm_discrete(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    acquisition_function=acquisition_function,
                    axus=axus,
                    trust_region=trust_region,
                    x_center=x_center,
                    device="cpu",
                    seed=42 + run,
                    **config["params"]
                )
                diversity_histories.append(algorithm_state.get("diversity_history", []))
            else:
                x_best, fx_best, _ = run_genetic_algorithm_discrete(
                    x_scaled=x_scaled,
                    fx_scaled=fx_scaled,
                    acquisition_function=acquisition_function,
                    model=model,
                    axus=axus,
                    trust_region=trust_region,
                    device="cpu",
                    **config["params"]
                )
                diversity_histories.append([])
            
            end_time = time.time()
            
            run_time = end_time - start_time
            best_value = fx_best.item()
            
            run_times.append(run_time)
            best_values.append(best_value)
            
            print(f"  运行时间: {run_time:.3f}s")
            print(f"  最优值: {best_value:.6f}")
        
        # 统计结果
        avg_time = np.mean(run_times)
        std_time = np.std(run_times)
        avg_best = np.mean(best_values)
        std_best = np.std(best_values)
        
        results[config['name']] = {
            'avg_time': avg_time,
            'std_time': std_time,
            'avg_best': avg_best,
            'std_best': std_best,
            'diversity_histories': diversity_histories
        }
        
        print(f"\n{config['name']} 总结:")
        print(f"  平均运行时间: {avg_time:.3f} ± {std_time:.3f}s")
        print(f"  平均最优值: {avg_best:.6f} ± {std_best:.6f}")
    
    # 打印对比结果
    print("\n" + "=" * 80)
    print("增强GA性能对比结果")
    print("=" * 80)
    print(f"{'配置':<25} {'平均时间(s)':<15} {'平均最优值':<15} {'时间改进':<10} {'质量改进':<10}")
    print("-" * 80)
    
    baseline_name = "原始GA"
    baseline = results[baseline_name]
    
    for name, result in results.items():
        time_improvement = ((baseline['avg_time'] - result['avg_time']) / baseline['avg_time']) * 100
        quality_improvement = ((baseline['avg_best'] - result['avg_best']) / baseline['avg_best']) * 100
        
        print(f"{name:<25} {result['avg_time']:.3f}±{result['std_time']:.3f}     "
              f"{result['avg_best']:.4f}±{result['std_best']:.4f}   "
              f"{time_improvement:+.1f}%       {quality_improvement:+.1f}%")
    
    # 输出多样性分析
    analyze_diversity_results(results)

    return results

def analyze_diversity_results(results: Dict):
    """分析多样性结果"""
    print("\n" + "=" * 60)
    print("多样性分析结果")
    print("=" * 60)

    for name, result in results.items():
        diversity_histories = result['diversity_histories']
        if diversity_histories and any(len(h) > 0 for h in diversity_histories):
            # 计算平均多样性统计
            all_diversities = []
            for history in diversity_histories:
                all_diversities.extend(history)

            if all_diversities:
                avg_diversity = np.mean(all_diversities)
                std_diversity = np.std(all_diversities)
                min_diversity = np.min(all_diversities)
                max_diversity = np.max(all_diversities)

                print(f"\n{name}:")
                print(f"  平均多样性: {avg_diversity:.4f} ± {std_diversity:.4f}")
                print(f"  多样性范围: [{min_diversity:.4f}, {max_diversity:.4f}]")

                # 分析多样性趋势
                if len(diversity_histories[0]) > 10:
                    early_avg = np.mean([h[:5] for h in diversity_histories if len(h) >= 5])
                    late_avg = np.mean([h[-5:] for h in diversity_histories if len(h) >= 5])
                    trend = "下降" if early_avg > late_avg else "上升" if late_avg > early_avg else "稳定"
                    print(f"  多样性趋势: {trend} (早期: {early_avg:.4f} -> 后期: {late_avg:.4f})")
        else:
            print(f"\n{name}: 无多样性数据")

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    results = run_comparison_test()
    
    print("\n测试完成！")
