#!/usr/bin/env python3
"""
简单测试增强GA的基本功能
"""

import torch
import numpy as np
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("测试增强GA的基本功能...")
    
    try:
        from enhanced_discrete_ga import run_enhanced_genetic_algorithm_discrete
        from adaptive_diversity_management import (
            AdaptiveDiversityManager, 
            GlobalSolutionArchive,
            PopulationDiversityMonitor,
            DiversityAwareSelection
        )
        
        print("✅ 成功导入所有模块")
        
        # 测试全局解库
        archive = GlobalSolutionArchive(max_size=100, diversity_threshold=0.1)

        # 添加一些解
        solutions = torch.rand(5, 10)
        fitness_values = torch.rand(5)

        for i in range(5):
            archive.add_solution(solutions[i], fitness_values[i].item(), generation=0)
        
        print(f"✅ 解库测试通过，当前大小: {len(archive.archive)}")
        
        # 测试多样性监控
        monitor = PopulationDiversityMonitor()
        population = torch.rand(20, 10)
        diversity = monitor.calculate_population_diversity(population)
        
        print(f"✅ 多样性监控测试通过，多样性值: {diversity:.4f}")
        
        # 测试多样性感知选择
        selector = DiversityAwareSelection(diversity_weight=0.3)
        fitness = torch.rand(20)
        selected_pop, selected_fitness = selector.select_with_diversity(population, fitness, selection_size=10)
        
        print(f"✅ 多样性选择测试通过，选择了 {len(selected_pop)} 个个体")
        
        # 测试自适应多样性管理器
        manager = AdaptiveDiversityManager(
            archive_size=100,
            diversity_threshold=0.1
        )
        
        # 模拟一代进化
        update_result = manager.update_with_population(population, fitness, generation=5)
        should_inject = manager.should_inject_diversity(update_result["diversity_status"])
        
        print(f"✅ 自适应管理器测试通过，是否注入: {should_inject}")
        
        print("🎉 所有基本功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_return_format():
    """测试返回格式"""
    print("\n测试返回格式...")
    
    try:
        # 创建模拟数据
        x_scaled = torch.rand(10, 5)
        fx_scaled = torch.rand(10)
        x_center = torch.rand(5)
        
        # 模拟AxUS
        class MockAxUS:
            def __init__(self):
                self.target_dim = 5
                
            def n_bins_of_type(self, parameter_type):
                return 0  # 避免复杂的初始化
                
        # 模拟TrustRegion
        class MockTrustRegion:
            def __init__(self):
                self.length_discrete = 2
        
        # 模拟采集函数
        def mock_acquisition_function(x):
            return torch.sum(x**2, dim=-1)
        
        from enhanced_discrete_ga import run_enhanced_genetic_algorithm_discrete
        
        result = run_enhanced_genetic_algorithm_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=mock_acquisition_function,
            axus=MockAxUS(),
            trust_region=MockTrustRegion(),
            x_center=x_center,
            device="cpu",
            population_size=20,  # 增加种群大小避免交叉操作错误
            generations=3,
            batch_size=1,
            enable_adaptive_diversity=True
        )
        
        x_best, fx_best, state = result
        
        print(f"✅ 返回格式测试通过!")
        print(f"x_best形状: {x_best.shape}")
        print(f"fx_best形状: {fx_best.shape}")
        print(f"状态信息键: {list(state.keys())}")
        
        # 检查格式
        assert len(x_best.shape) == 2, f"x_best应该是2D，实际是{x_best.shape}"
        assert x_best.shape[0] == 1, f"batch_size应该是1，实际是{x_best.shape[0]}"
        assert x_best.shape[1] == 5, f"target_dim应该是5，实际是{x_best.shape[1]}"
        
        print("✅ 格式检查通过!")
        return True
        
    except Exception as e:
        print(f"❌ 返回格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    torch.manual_seed(42)
    np.random.seed(42)
    
    success1 = test_basic_functionality()
    success2 = test_return_format()
    
    if success1 and success2:
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 部分测试失败")
