#!/usr/bin/env python3
"""
测试增强GA的兼容性
"""

import torch
import numpy as np
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_ga_compatible import run_genetic_algorithm_discrete

def test_compatibility():
    """测试兼容性"""
    print("测试增强GA的兼容性...")
    
    # 模拟AxUS和TrustRegion对象
    class MockAxUS:
        def __init__(self):
            self.target_dim = 10
            
        def n_bins_of_type(self, parameter_type):
            return 10
            
        def bins_and_indices_of_type(self, parameter_type):
            return [(i, i) for i in range(10)]
            
    class MockTrustRegion:
        def __init__(self):
            self.length_discrete = 2  # 使用整数
    
    class MockModel:
        def __init__(self):
            pass
    
    # 模拟采集函数
    def mock_acquisition_function(x):
        x_np = x.squeeze().cpu().numpy()
        if x_np.ndim == 1:
            x_np = x_np.reshape(1, -1)
        
        results = []
        for sample in x_np:
            result = np.sum(sample**2)
            results.append(result)
        
        return torch.tensor(results, dtype=torch.float32)
    
    # 创建测试数据
    axus = MockAxUS()
    trust_region = MockTrustRegion()
    model = MockModel()
    
    x_scaled = torch.rand(20, axus.target_dim)
    fx_scaled = mock_acquisition_function(x_scaled)
    
    print(f"输入数据形状: x_scaled={x_scaled.shape}, fx_scaled={fx_scaled.shape}")
    
    try:
        # 测试增强版GA
        result = run_genetic_algorithm_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=mock_acquisition_function,
            model=model,
            axus=axus,
            trust_region=trust_region,
            device="cpu",
            batch_size=1,
            population_size=20,
            n_generations=5,
            enable_adaptive_diversity=True
        )
        
        x_best, fx_best, state = result
        
        print(f"✅ 测试成功!")
        print(f"返回结果形状: x_best={x_best.shape}, fx_best={fx_best.shape}")
        print(f"x_best维度: {x_best.shape}")
        print(f"fx_best维度: {fx_best.shape}")
        print(f"状态信息键: {list(state.keys())}")
        
        # 检查返回格式
        assert len(x_best.shape) == 2, f"x_best应该是2D，实际是{x_best.shape}"
        assert x_best.shape[0] == 1, f"batch_size应该是1，实际是{x_best.shape[0]}"
        assert x_best.shape[1] == axus.target_dim, f"target_dim应该是{axus.target_dim}，实际是{x_best.shape[1]}"
        
        print("✅ 格式检查通过!")
        
        # 测试是否启用了增强功能
        if 'diversity_history' in state:
            print("✅ 增强功能已启用")
            print(f"多样性历史长度: {len(state.get('diversity_history', []))}")
            print(f"注入历史: {state.get('injection_history', [])}")
        else:
            print("⚠️ 增强功能未启用")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n测试向后兼容性...")
    
    # 模拟对象
    class MockAxUS:
        def __init__(self):
            self.target_dim = 10
            
        def n_bins_of_type(self, parameter_type):
            return 10
            
        def bins_and_indices_of_type(self, parameter_type):
            return [(i, i) for i in range(10)]
            
    class MockTrustRegion:
        def __init__(self):
            self.length_discrete = 2  # 使用整数
    
    class MockModel:
        def __init__(self):
            pass
    
    def mock_acquisition_function(x):
        x_np = x.squeeze().cpu().numpy()
        if x_np.ndim == 1:
            x_np = x_np.reshape(1, -1)
        return torch.tensor([np.sum(sample**2) for sample in x_np], dtype=torch.float32)
    
    axus = MockAxUS()
    trust_region = MockTrustRegion()
    model = MockModel()
    x_scaled = torch.rand(20, axus.target_dim)
    fx_scaled = mock_acquisition_function(x_scaled)
    
    try:
        # 测试禁用增强功能（向后兼容）
        result = run_genetic_algorithm_discrete(
            x_scaled=x_scaled,
            fx_scaled=fx_scaled,
            acquisition_function=mock_acquisition_function,
            model=model,
            axus=axus,
            trust_region=trust_region,
            device="cpu",
            batch_size=1,
            population_size=20,
            n_generations=5,
            enable_adaptive_diversity=False  # 禁用增强功能
        )
        
        x_best, fx_best, state = result
        print(f"✅ 向后兼容测试成功!")
        print(f"返回结果形状: x_best={x_best.shape}, fx_best={fx_best.shape}")
        
    except Exception as e:
        print(f"❌ 向后兼容测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    torch.manual_seed(42)
    np.random.seed(42)
    
    success1 = test_compatibility()
    success2 = test_backward_compatibility()
    
    if success1 and success2:
        print("\n🎉 所有兼容性测试通过!")
    else:
        print("\n❌ 部分测试失败")
