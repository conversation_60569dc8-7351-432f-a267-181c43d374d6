#!/usr/bin/env python3
"""
全面测试改进的自适应差分进化算法
包括性能对比、收敛分析和效率测试
"""

import torch
import numpy as np
import time
from bounce_ga.candidates_ga import run_differential_evolution_continuous
from bounce.projection import AxUS
from bounce.trust_region import TrustRegion
from bounce.util.benchmark import Parameter, ParameterType
from botorch.models import SingleTaskGP
from botorch.acquisition import ExpectedImprovement
from gpytorch.kernels import MaternKernel, ScaleKernel
from gpytorch.means import ConstantMean
from gpytorch.likelihoods import GaussianLikelihood
from gpytorch.mlls import ExactMarginalLogLikelihood
from botorch.fit import fit_gpytorch_model

def ackley_function(x: torch.Tensor) -> torch.Tensor:
    """
    Ackley函数：另一个多模态测试函数
    f(x) = -20*exp(-0.2*sqrt(1/n * sum(x_i^2))) - exp(1/n * sum(cos(2*pi*x_i))) + 20 + e
    """
    # 将 [0,1] 映射到 [-5, 5]
    x_mapped = (x - 0.5) * 10
    
    n = x.shape[1]
    term1 = -20 * torch.exp(-0.2 * torch.sqrt(torch.mean(x_mapped**2, dim=1)))
    term2 = -torch.exp(torch.mean(torch.cos(2 * np.pi * x_mapped), dim=1))
    
    return term1 + term2 + 20 + np.e

def rosenbrock_function(x: torch.Tensor) -> torch.Tensor:
    """
    Rosenbrock函数：经典的优化测试函数
    f(x) = sum(100*(x_{i+1} - x_i^2)^2 + (1 - x_i)^2)
    """
    # 将 [0,1] 映射到 [-2, 2]
    x_mapped = (x - 0.5) * 4
    
    result = torch.zeros(x.shape[0])
    for i in range(x.shape[1] - 1):
        result += 100 * (x_mapped[:, i+1] - x_mapped[:, i]**2)**2 + (1 - x_mapped[:, i])**2
    
    return result

def create_test_setup(target_dim: int, test_function, n_points: int = 20):
    """创建测试设置"""
    device = "cpu"
    
    # 创建参数
    parameters = []
    for i in range(target_dim):
        param = Parameter(
            name=f'x{i}',
            type=ParameterType.CONTINUOUS,
            lower_bound=-5.0,
            upper_bound=5.0,
            random_sign=1
        )
        parameters.append(param)
    
    # 创建 AxUS 对象
    axus = AxUS(parameters=parameters, n_bins=target_dim)
    
    # 创建信赖域
    trust_region = TrustRegion(dimensionality=target_dim)
    
    # 生成训练数据
    x_scaled = torch.rand(n_points, target_dim, dtype=torch.double)
    fx_scaled = test_function(x_scaled)
    
    # 标准化函数值
    mean = torch.mean(fx_scaled)
    std = torch.std(fx_scaled)
    if std == 0:
        std = 1.0
    fx_scaled_norm = (fx_scaled - mean) / std
    
    # 创建 GP 模型
    likelihood = GaussianLikelihood()
    model = SingleTaskGP(
        train_X=x_scaled,
        train_Y=-fx_scaled_norm.unsqueeze(-1).double(),
        likelihood=likelihood,
        mean_module=ConstantMean(),
        covar_module=ScaleKernel(MaternKernel(nu=2.5))
    )
    
    # 拟合模型
    mll = ExactMarginalLogLikelihood(likelihood, model)
    fit_gpytorch_model(mll)
    
    # 创建采集函数
    acquisition_function = ExpectedImprovement(
        model=model, 
        best_f=(-fx_scaled_norm).max().item()
    )
    
    return {
        'x_scaled': x_scaled,
        'fx_scaled_norm': fx_scaled_norm,
        'acquisition_function': acquisition_function,
        'model': model,
        'axus': axus,
        'trust_region': trust_region,
        'device': device,
        'test_function': test_function
    }

def run_performance_comparison():
    """运行性能对比测试"""
    
    print("=" * 80)
    print("自适应差分进化算法 - 全面性能测试")
    print("=" * 80)
    
    # 测试函数
    test_functions = [
        ("Ackley", ackley_function, 5),
        ("Rosenbrock", rosenbrock_function, 4),
    ]
    
    # 测试配置
    configs = [
        {
            "name": "基础DE",
            "adaptive_params": False,
            "use_crowding_distance": False,
            "enable_early_stopping": False,
            "adaptive_population": False,
            "batch_evaluation": False,
            "population_size": 50,
            "n_generations": 30
        },
        {
            "name": "自适应DE",
            "adaptive_params": True,
            "use_crowding_distance": False,
            "enable_early_stopping": False,
            "adaptive_population": False,
            "batch_evaluation": False,
            "population_size": 50,
            "n_generations": 30
        },
        {
            "name": "多样性保持DE",
            "adaptive_params": True,
            "use_crowding_distance": True,
            "diversity_threshold": 1e-4,
            "stagnation_threshold": 5,
            "restart_ratio": 0.3,
            "enable_early_stopping": False,
            "adaptive_population": False,
            "batch_evaluation": False,
            "population_size": 50,
            "n_generations": 30
        },
        {
            "name": "全功能DE",
            "adaptive_params": True,
            "use_crowding_distance": True,
            "diversity_threshold": 1e-4,
            "stagnation_threshold": 5,
            "restart_ratio": 0.3,
            "enable_early_stopping": True,
            "early_stopping_patience": 5,
            "adaptive_population": True,
            "min_population_size": 20,
            "batch_evaluation": True,
            "evaluation_batch_size": 10,
            "population_size": 50,
            "n_generations": 30
        }
    ]
    
    results = {}
    
    for func_name, test_func, dim in test_functions:
        print(f"\n测试函数: {func_name} (维度: {dim})")
        print("-" * 60)
        
        setup = create_test_setup(dim, test_func)
        results[func_name] = {}
        
        for config in configs:
            config_name = config['name']
            print(f"\n配置: {config_name}")
            
            try:
                start_time = time.time()
                
                x_best, fx_best, tr_state = run_differential_evolution_continuous(
                    x_scaled=setup['x_scaled'],
                    fx_scaled=setup['fx_scaled_norm'],
                    acquisition_function=setup['acquisition_function'],
                    model=setup['model'],
                    axus=setup['axus'],
                    trust_region=setup['trust_region'],
                    device=setup['device'],
                    batch_size=2,
                    **{k: v for k, v in config.items() if k != 'name'}
                )
                
                end_time = time.time()
                runtime = end_time - start_time
                
                # 评估真实函数值
                x_best_scaled = (x_best + 1) / 2
                true_fx = test_func(x_best_scaled)
                best_value = true_fx.min().item()
                
                results[func_name][config_name] = {
                    'best_value': best_value,
                    'runtime': runtime,
                    'success': True
                }
                
                print(f"  最优值: {best_value:.6f}")
                print(f"  运行时间: {runtime:.3f}s")
                print(f"  ✓ 成功")
                
            except Exception as e:
                print(f"  ✗ 失败: {e}")
                results[func_name][config_name] = {
                    'best_value': float('inf'),
                    'runtime': float('inf'),
                    'success': False
                }
    
    # 输出汇总结果
    print("\n" + "=" * 80)
    print("性能汇总")
    print("=" * 80)
    
    for func_name in results:
        print(f"\n{func_name} 函数:")
        print(f"{'配置':<20} {'最优值':<12} {'运行时间(s)':<12} {'状态'}")
        print("-" * 50)
        
        for config_name, result in results[func_name].items():
            if result['success']:
                status = "✓"
                best_val = f"{result['best_value']:.6f}"
                runtime = f"{result['runtime']:.3f}"
            else:
                status = "✗"
                best_val = "失败"
                runtime = "失败"
            
            print(f"{config_name:<20} {best_val:<12} {runtime:<12} {status}")
    
    return results

if __name__ == "__main__":
    results = run_performance_comparison()
