import logging
import warnings
from typing import Optional

import gin
import math
import random
import numpy as np
import torch

# 这两个是贝叶斯优化中非常重要的采集函数（acquisition function）
from botorch.acquisition import ExpectedImprovement, qExpectedImprovement

# 高斯过程模型类，用于单目标（single-objective）优化问题
from botorch.models import SingleTaskGP

# 优化采集函数的函数
from botorch.optim import optimize_acqf

# 使用 Sobol 序列采样，用于构造采集函数中的“近似后验样本”。
from botorch.sampling import SobolQMCNormalSampler

# 常用的平滑核函数
from gpytorch.kernels import MaternKernel

from bounce.kernel.categorical_mixture import MixtureKernel
# 自定义邻居计算工具：hamming_distance：汉明距离 hamming_neighbors_within_tr：在信赖域（TR）中找邻居点
from bounce.neighbors import hamming_distance, hamming_neighbors_within_tr
from bounce.projection import AxUS
from bounce.trust_region import TrustRegion
from bounce.util.benchmark import ParameterType

@gin.configurable
def run_genetic_algorithm_discrete(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int = 1,
    x_bests: Optional[list[torch.Tensor]] = None,           # 外部传进来的“最好点”，用于混合变量情况的连续变量部分替换
    population_size: int = 5000,
    n_generations: int = 20,
    crossover_rate: float = 0.8,
    mutation_rate: float = 0.2,
    sampler: Optional[SobolQMCNormalSampler] = None,
    # GP方差和随机解注入参数
    enable_gp_variance_injection: bool = True,
    gp_variance_ratio: float = 0.1,
    variance_objective_weight: float = 0.3,
    enable_random_injection: bool = True,
    random_injection_ratio: float = 0.05,
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    使用遗传算法在低维嵌入空间中搜索最优解。

    Args:
        x_scaled: 当前信赖域样本点 (n, d)
        fx_scaled: 当前样本点函数值 (n,)
        acquisition_function: 用于 batch_size=1 的 EI 函数
        model: 拟合的 GP 代理模型
        axus: 嵌入对象
        trust_region: 当前信赖域
        device: 运行设备
        batch_size: 要返回的候选解个数
        x_best: 交替优化后传进来的“最好点”，可能后续用于初始化种群
        population_size: 每一代的种群大小
        n_generations: 进化代数
        crossover_rate: 交叉概率
        mutation_rate: 变异概率
        sampler: 用于 batch_size > 1 时的 qEI 采样器

    Returns:
        x_best: 最佳低维点（batch_size, target_dim）
        fx_best: 对应的 EI 或预测值（batch_size）
        tr_state: 当前信赖域状态
    """
    def evaluate(population , x_pending = None):
        """
            用 GP 或 acquisition function 评估适应度
            TODO 如果仅仅使用GP呢？
        """
        with torch.no_grad():
            if acquisition_function is not None:
                assert (
                    sampler is None
                ), "Either acquisition_function or sampler must be provided"
                return -acquisition_function(population.unsqueeze(1)).squeeze()
            else:
                _acq_fn = qExpectedImprovement(
                    model=model,
                    best_f=(-fx_scaled).max().item(),
                    sampler=sampler,
                    X_pending=x_pending
                )
                return -_acq_fn(population.unsqueeze(1)).squeeze()
            
    # 确保种群大小合理，但不覆盖用户设定的参数
    max_population_size = min(5000, max(2000, 200 * axus.target_dim))
    actual_population_size = min(population_size, max_population_size)
    if actual_population_size != population_size:
        print(f"⚠️ 种群大小从 {population_size} 调整为 {actual_population_size}")

    print(f"🧬 遗传算法参数: 种群大小={actual_population_size}, 代数={n_generations}, 交叉率={crossover_rate}, 变异率={mutation_rate}")

    # 从 AxUS 中提取出“连续变量”的所有列索引。
    # （就是低维点中连续的索引）例如一个点索引（0,1,2,3,4,5）返回[2][4]就表示索引2，4是连续的部分
    indices_not_to_optimize = torch.tensor(
        [i for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)]
    )

    # 找出当前函数值 fx_scaled 最小的那个点（最优点）作为 TR 中心点；
    x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()

    x_center = x_centers.clone()

    # 然后复制 batch_size 份，作为每个 batch 任务的初始点。
    x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)

    # 如果用户提供了 x_bests（通常是之前 continuous 优化的结果），我们就“覆盖”掉中心点中连续变量那几列。
    if x_bests is not None:
        # replace
        x_centers[:, indices_not_to_optimize] = (
            x_bests[:, indices_not_to_optimize] + 1
        ) / 2

    # ==================== 标准遗传算法框架 ====================

    # 1. 初始化种群
    print(f"🔄 第0代: 初始化种群...")
    population = initialize_population(
        axus=axus,
        x_center=x_center,
        trust_region=trust_region,
        population_size=actual_population_size,
        device=device,
        x_bests=x_bests
    )
    # 2. 评估初始种群
    fitness = evaluate(population)
    print(f"📊 初始种群: 大小={len(population)}, 最佳适应度={fitness.min().item():.4f}")

    # 3. 遗传算法主循环
    elite_size = max(1, math.floor(actual_population_size * 0.05))

    for generation in range(n_generations):
        print(f"🔄 第{generation+1}代: 开始进化...")

        # 3.1 选择和繁殖 - 生成更多子代
        offspring_size = actual_population_size  # 生成与种群相同数量的子代
        children = generate_offspring(
            population=population,
            fitness=fitness,
            offspring_size=offspring_size,
            axus=axus,  # 添加axus参数
            crossover_rate=crossover_rate,
            mutation_rate=mutation_rate,
            elite_size=elite_size
        )

        print(f"👶 生成子代: {len(children)}个")

        # 3.2 合并父代和子代
        total_population = torch.cat([population, children], dim=0)
        total_fitness = evaluate(total_population)

        print(f"🔗 合并种群: 父代{len(population)} + 子代{len(children)} = 总计{len(total_population)}")

        # 3.3 环境选择 - 选择下一代
        if len(total_population) > actual_population_size:
            population = enhanced_survivor_selection(
                total_population=total_population,
                total_fitness=total_fitness,
                population_size=actual_population_size,
                elite_size=elite_size,
                model=model,
                x_center=x_center,
                trust_region=trust_region,
                axus=axus,
                device=device,
                enable_gp_variance_injection=enable_gp_variance_injection,
                gp_variance_ratio=gp_variance_ratio,
                variance_objective_weight=variance_objective_weight,
                enable_random_injection=enable_random_injection,
                random_injection_ratio=random_injection_ratio,
                x_bests=x_bests
            )
        else:
            population = total_population

        # 3.4 重新评估新种群
        fitness = evaluate(population)
        best_fitness = fitness.min().item()
        print(f"✅ 第{generation+1}代完成: 种群大小={len(population)}, 最佳适应度={best_fitness:.4f}")

    print(f"🏁 遗传算法完成: 总共{n_generations}代, 最终种群大小={len(population)}")
    
    # 选出最好的 batch_size 个候选点
    best_indices = torch.topk(-fitness, k=batch_size).indices
    x_batch_return = population[best_indices]  # shape: (batch_size, d)
    fx_batch_return = fitness[best_indices]
    # 数据已经在[-1,1]范围内，无需转换

    # 构造 trust region 状态记录
    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_discrete]),  # 当前离散长度
    }

    # 返回最终候选点、采集函数值、tr记录
    return x_batch_return, fx_batch_return, tr_state

def enhanced_survivor_selection(
    total_population: torch.Tensor,
    total_fitness: torch.Tensor,
    population_size: int,
    elite_size: int,
    model,
    x_center: torch.Tensor,
    trust_region,
    axus,
    device: str,
    enable_gp_variance_injection: bool = True,
    gp_variance_ratio: float = 0.1,
    variance_objective_weight: float = 0.3,
    enable_random_injection: bool = True,
    random_injection_ratio: float = 0.05,
    x_bests: Optional[torch.Tensor] = None
) -> torch.Tensor:
    """
    增强的环境选择，结合适应度选择、GP方差注入和随机解注入
    """
    # 基础适应度选择的数量
    base_selection_size = population_size

    # 计算GP方差注入的数量
    gp_variance_count = 0
    if enable_gp_variance_injection and model is not None:
        gp_variance_count = max(1, int(population_size * gp_variance_ratio))
        base_selection_size -= gp_variance_count

    # 计算随机解注入的数量
    random_injection_count = 0
    if enable_random_injection:
        random_injection_count = max(1, int(population_size * random_injection_ratio))
        base_selection_size -= random_injection_count

    # 确保基础选择数量不为负
    base_selection_size = max(elite_size, base_selection_size)

    # 1. 基础适应度选择
    selected_population = survivor_selection(
        total_population, total_fitness, base_selection_size, elite_size
    )

    # 2. GP方差注入
    if gp_variance_count > 0 and model is not None:
        try:
            # 生成候选解
            n_candidates = min(1000, max(200, 50 * axus.target_dim))
            variance_candidates = sample_initial_points_discrete(
                x_center=x_center,
                tr_length=trust_region.length_discrete,
                axus=axus,
                n_initial_points=n_candidates
            )
            if len(variance_candidates) > 0:
                # 计算GP方差
                with torch.no_grad():
                    # 转换到[-1,1]空间进行GP预测
                    variance_candidates_scaled = variance_candidates * 2 - 1
                    posterior = model.posterior(variance_candidates_scaled)
                    variances = posterior.variance.squeeze()
                    means = posterior.mean.squeeze()

                # 归一化方差和均值
                if len(variances) > 1:
                    variances_norm = (variances - variances.min()) / (variances.max() - variances.min() + 1e-8)
                    means_norm = (means - means.min()) / (means.max() - means.min() + 1e-8)

                    # 组合得分：平衡方差和目标值
                    combined_scores = (1 - variance_objective_weight) * (-means_norm) + variance_objective_weight * variances_norm

                    # 选择得分最高的解
                    top_indices = torch.topk(combined_scores, min(gp_variance_count, len(combined_scores))).indices
                    gp_variance_solutions = variance_candidates[top_indices]

                    # 添加到选择的种群中
                    selected_population = torch.cat([selected_population, gp_variance_solutions], dim=0)
        except Exception as e:
            print(f"GP方差注入失败: {e}")

    # 3. 随机解注入
    if random_injection_count > 0:
        try:
            random_solutions = sample_initial_points_discrete(
                x_center=x_center,
                tr_length=trust_region.length_discrete,
                axus=axus,
                n_initial_points=random_injection_count
            )

            if len(random_solutions) > 0:
                # 取前random_injection_count个
                random_solutions = random_solutions[:random_injection_count]
                selected_population = torch.cat([selected_population, random_solutions], dim=0)
        except Exception as e:
            print(f"随机解注入失败: {e}")

    # 如果总数超过population_size，进行最终选择
    if len(selected_population) > population_size:
        # 重新评估适应度并选择最好的
        final_fitness = torch.zeros(len(selected_population))
        with torch.no_grad():
            if hasattr(model, '__call__'):
                # 使用acquisition function评估
                selected_scaled = selected_population * 2 - 1
                final_fitness = -model(selected_scaled.unsqueeze(1)).squeeze()
            else:
                # 简单的随机选择
                indices = torch.randperm(len(selected_population))[:population_size]
                return selected_population[indices]

        # 选择最好的population_size个
        best_indices = torch.topk(-final_fitness, population_size).indices
        selected_population = selected_population[best_indices]

    return selected_population


def survivor_selection(
    total_population: torch.Tensor,
    total_fitness: torch.Tensor,
    population_size: int,
    elite_size: int = 1
) -> torch.Tensor:
    """
    从父代和子代中选出适应度最高的个体组成新一代种群。

    参数：
        parents: 父代种群 tensor，形状 (N, d)
        children: 子代种群 tensor，形状 (M, d)
        parent_fitness: 父代适应度，形状 (N,)
        child_fitness: 子代适应度，形状 (M,)
        population_size: 下一代种群总数量
        elite_size: 精英个体数，默认保留最好的 parent

    返回：
        new_population: 下一代种群，形状 (population_size, d)
    """

    # 找出精英个体索引（适应度最小）
    elite_indices = torch.topk(-total_fitness, elite_size).indices
    elite = total_population[elite_indices]

    # 排除精英，剩下的从中选出补充个体
    mask = torch.ones(total_population.shape[0], dtype=torch.bool)
    mask[elite_indices] = False
    remaining_population = total_population[mask]
    remaining_fitness = total_fitness[mask]

    # 选出剩下最好的 individuals
    top_k = population_size - elite_size
    top_indices = torch.topk(-remaining_fitness, top_k).indices
    survivors = remaining_population[top_indices]

    return torch.vstack((elite, survivors))


def generate_offspring(
    population: torch.Tensor,
    fitness: torch.Tensor,
    offspring_size: int,
    axus,  # 添加axus参数以获取变量类型信息
    crossover_rate: float = 0.8,
    mutation_rate: float = 0.2,
    elite_size: int = 1
) -> torch.Tensor:
    """
    生成子代种群 - 考虑不同变量类型的特殊处理

    Args:
        population: 父代种群 (pop_size, dim)
        fitness: 父代适应度 (pop_size,)
        offspring_size: 要生成的子代数量
        axus: AxUS对象，用于获取变量类型信息
        crossover_rate: 交叉概率
        mutation_rate: 变异概率
        elite_size: 精英个体数量

    Returns:
        children: 子代种群 (offspring_size, dim)
    """
    device = population.device
    pop_size, dim = population.shape

    # 获取不同类型变量的索引信息
    continuous_indices = []
    binary_indices = []
    categorical_groups = []  # 每个分类变量的one-hot索引组

    # 连续变量索引
    for bin, indices in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS):
        continuous_indices.extend(indices.tolist())

    # 二分变量索引
    for bin, indices in axus.bins_and_indices_of_type(ParameterType.BINARY):
        binary_indices.extend(indices.tolist())

    # 分类变量索引组（每个分类变量是一个one-hot编码组）
    for bin, indices in axus.bins_and_indices_of_type(ParameterType.CATEGORICAL):
        categorical_groups.append(indices.tolist())

    continuous_indices = torch.tensor(continuous_indices, device=device) if continuous_indices else torch.tensor([], device=device, dtype=torch.long)
    binary_indices = torch.tensor(binary_indices, device=device) if binary_indices else torch.tensor([], device=device, dtype=torch.long)

    # 锦标赛选择函数
    def tournament_selection(k=3):
        """锦标赛选择"""
        indices = torch.randint(0, pop_size, (k,), device=device)
        tournament_fitness = fitness[indices]
        winner_idx = indices[torch.argmin(tournament_fitness)]  # 适应度越小越好
        return population[winner_idx]

    def type_aware_crossover(parent1: torch.Tensor, parent2: torch.Tensor) -> torch.Tensor:
        """考虑变量类型的交叉操作"""
        child = parent1.clone()

        # 连续变量：单点交叉
        if len(continuous_indices) > 0:
            if len(continuous_indices) > 1:
                crossover_point = torch.randint(0, len(continuous_indices), (1,), device=device).item()
                mask = torch.zeros(len(continuous_indices), dtype=torch.bool, device=device)
                mask[crossover_point:] = True
                child[continuous_indices[mask]] = parent2[continuous_indices[mask]]

        # 二分变量：均匀交叉
        if len(binary_indices) > 0:
            crossover_mask = torch.rand(len(binary_indices), device=device) < 0.5
            child[binary_indices[crossover_mask]] = parent2[binary_indices[crossover_mask]]

        # 分类变量：整个one-hot组交叉
        for cat_indices in categorical_groups:
            if torch.rand(1, device=device).item() < 0.5:  # 50%概率交换整个分类变量
                cat_indices_tensor = torch.tensor(cat_indices, device=device)
                child[cat_indices_tensor] = parent2[cat_indices_tensor]

        return child

    def type_aware_mutation(individual: torch.Tensor) -> torch.Tensor:
        """考虑变量类型的变异操作"""
        mutated = individual.clone()

        # 连续变量变异：高斯噪声
        if len(continuous_indices) > 0 and torch.rand(1, device=device).item() < mutation_rate:
            mutation_indices = continuous_indices[torch.rand(len(continuous_indices), device=device) < 0.1]  # 10%的连续变量变异
            if len(mutation_indices) > 0:
                noise = torch.randn(len(mutation_indices), device=device) * 0.1
                mutated[mutation_indices] = torch.clamp(mutated[mutation_indices] + noise, -1, 1)

        # 二分变量变异：翻转
        if len(binary_indices) > 0 and torch.rand(1, device=device).item() < mutation_rate:
            mutation_indices = binary_indices[torch.rand(len(binary_indices), device=device) < 0.05]  # 5%的二分变量变异
            if len(mutation_indices) > 0:
                mutated[mutation_indices] = -mutated[mutation_indices]  # 在[-1,1]中翻转

        # 分类变量变异：重新随机选择类别
        for cat_indices in categorical_groups:
            if torch.rand(1, device=device).item() < mutation_rate * 0.5:  # 降低分类变量变异率
                cat_indices_tensor = torch.tensor(cat_indices, device=device)
                # 清零当前one-hot
                mutated[cat_indices_tensor] = -1  # 在[-1,1]编码中，-1表示未选中
                # 随机选择一个新类别
                selected_idx = torch.randint(0, len(cat_indices), (1,), device=device).item()
                mutated[cat_indices_tensor[selected_idx]] = 1  # 1表示选中

        return mutated

    def ensure_categorical_constraints(individual: torch.Tensor) -> torch.Tensor:
        """确保分类变量满足one-hot约束"""
        corrected = individual.clone()

        for cat_indices in categorical_groups:
            cat_indices_tensor = torch.tensor(cat_indices, device=device)
            cat_values = corrected[cat_indices_tensor]

            # 检查是否有且仅有一个1
            positive_count = (cat_values > 0).sum()

            if positive_count != 1:
                # 违反约束，重新设置
                corrected[cat_indices_tensor] = -1  # 全部设为-1
                # 随机选择一个位置设为1
                selected_idx = torch.randint(0, len(cat_indices), (1,), device=device).item()
                corrected[cat_indices_tensor[selected_idx]] = 1

        return corrected

    children = []

    # 生成子代
    for _ in range(offspring_size):
        if torch.rand(1, device=device).item() < crossover_rate:
            # 交叉操作
            parent1 = tournament_selection()
            parent2 = tournament_selection()
            child = type_aware_crossover(parent1, parent2)
        else:
            # 直接复制
            child = tournament_selection().clone()

        # 变异操作
        child = type_aware_mutation(child)

        # 确保分类变量约束
        child = ensure_categorical_constraints(child)

        children.append(child)

    return torch.stack(children)


def evolve_population(population, fitness, elite_size=1, selection_strategy='tournament'):
    """
    完成一代进化：选择、交叉、变异、精英保留

    Returns:
        新的种群（含精英）
    """
    assert len(population) == len(fitness), "population and fitness must have the same length"
    
    # 确保elite_size不超过种群大小
    elite_size = min(elite_size, len(population))
    
    elite_indices = torch.topk(-fitness, elite_size).indices
    elite = population[elite_indices]
 
    # 选择精英解
    parents = select_parents(population, 
                             fitness, 
                             elite_size, 
                             strategy=selection_strategy)

    # 精英解交叉生成子代
    children = crossover(parents)

    # 子代变异
    children = mutate(children)

    # 用最优秀个体替代前面几个（精英保留）
    children[:elite_size] = elite
    return children


def select_parents(population, fitness, num_parents, strategy='roulette', tournament_size=3):
    """
    选择操作（默认 tournament）

    Args:
        population: 当前种群，shape=(n, d)
        fitness: 当前种群对应适应度，shape=(n,)
        num_parents: 要选出多少个 parent（一般为种群数）
        strategy: 'tournament' or 'roulette'
        tournament_size: 锦标赛规模

    Returns:
        parents: shape=(num_parents, d)
    """
    selected = []
    n = population.shape[0]

    if strategy == 'tournament':
        for _ in range(num_parents):
            indices = np.random.choice(n, tournament_size, replace=False)
            winner_idx = indices[fitness[indices].argmin()]  # 最小的更好
            selected.append(population[winner_idx])
        return torch.stack(selected)
    elif strategy == 'roulette':
        fitness = fitness - fitness.min() + 1e-6  # 保证最小值为正
        probs = (1 / fitness).cpu().numpy()
        indices = np.random.choice(n, size=num_parents, p=probs)
        return population[indices]
    else:
        raise ValueError(f"Unknown selection strategy {strategy}")

def crossover(parents, crossover_rate=0.8):
    """
    单点交叉：将父代两两组合进行交叉

    Args:
        parents: shape=(n_parents, d)
        crossover_rate: 执行交叉的概率

    Returns:
        children: shape=(n_parents, d)
    """
    n = parents.shape[0]
    d = parents.shape[1]
    children = []

    for i in range(0, n, 2):
        p1 = parents[i]
        if i + 1 >= n:
            children.append(p1)
            continue
        p2 = parents[i + 1]

        if np.random.rand() < crossover_rate:
            point = np.random.randint(1, d)  # 随机交叉点
            c1 = torch.cat([p1[:point], p2[point:]])
            c2 = torch.cat([p2[:point], p1[point:]])
            children += [c1, c2]
        else:
            children += [p1, p2]
    return torch.stack(children[:n])

def mutate(children, mutation_rate=0.05):
    """
    逐位随机变异：binary flip / categorical 重新采样

    Args:
        children: shape=(n, d)

    Returns:
        mutated children
    """
    n, d = children.shape
    for i in range(n):
        for j in range(d):
            if np.random.rand() < mutation_rate:
                # 若是 binary，用 1 - x 实现 flip
                if children[i, j] in [0.0, 1.0]:  # 离散 one-hot、binary
                    children[i, j] = 1.0 - children[i, j]
                else:
                    # 连续变量：扰动
                    children[i, j] += np.random.normal(0, 0.1)
                    children[i, j] = torch.clamp(children[i, j], 0.0, 1.0)
    return children

def initialize_population(
    axus,
    x_center: torch.Tensor,
    trust_region,
    population_size: int,
    device: str,
    x_bests: Optional[torch.Tensor] = None,
    seed: Optional[int] = None,
) -> torch.Tensor:
    """
    初始化种群，包含 trust region 中心点，其他个体初始化，所有个体在 [0, 1]^d 中。

    Args:
        axus: AxUS 嵌入对象
        x_center: 当前 trust region 中心，shape: (target_dim,)
        trust_region: 当前 trust region 对象
        population_size: 种群数量
        device: 'cpu' 或 'cuda'
        x_bests: 历史最优点（预留）
        seed: 随机种子

    Returns:
        x_pop: shape (population_size, target_dim)，所有点在 [0, 1] 中
    """
    x_candidates = sample_initial_points_discrete(
            x_center=x_center,
            axus=axus,
            tr_length=trust_region.length_discrete,
            n_initial_points=population_size,
        )
        
    if seed is not None:
        torch.manual_seed(seed)
        np.random.seed(seed)

    # 如果x_best不为None，在x_best生成一部分汉明邻居
    if x_bests is not None:
        x_bests = (x_bests + 1)/2
        x_spray = hamming_neighbors_within_tr(
            x_center=x_center,
            x=x_bests,
            tr_length=trust_region.length_discrete,
            axus=axus,
        )
            # 按行拼接
        x_candidates = torch.vstack((x_candidates, x_spray))

    # 转换到[-1,1]范围，因为遗传算法在[-1,1]范围内工作
    return x_candidates * 2 - 1


    

@gin.configurable
def create_candidates_discrete(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int = 1,
    x_bests: Optional[list[torch.Tensor]] = None,           # 外部传进来的“最好点”，用于混合变量情况的连续变量部分替换
    add_spray_points: bool = True,                          # 是否添加与中心点汉明距离为 1 的点，默认为 True。这些点有助于局部搜索。
    sampler: Optional[SobolQMCNormalSampler] = None,
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    在当前信赖域内，使用采集函数 + 局部搜索，   
    它结合了贝叶斯优化和局部搜索策略，在当前信任域内寻找最有希望的点进行下一轮评估。
    为离散变量生成 batch_size 个候选点 x_best，并返回它们的采集函数值和信赖域状态。

    返回值：三元组 (x_batch_return, fx_batch_return, tr_state)：
        x_batch_return：生成的候选点，形状为 [batch_size, d]，范围为 [-1,1]。
        fx_batch_return：对应的采集函数值，形状为 [batch_size]。
        tr_state：信任域状态字典，包含中心点和半径信息。

    Create candidate points for the next batch.

    Args:
        model: The current GP model
        batch_size: The number of candidate points to create
        x_scaled: The current points in the trust region
        fx_scaled: The function values at the current points
        acquisition_function: The approximate posterior samples
        axus: The current AxUS embedding for the trust region
        trust_region: The current trust region state
        device: The device to use ('cpu' or 'cuda')
        x_bests: The center of the trust region, should be in [0, 1]^d
        add_spray_points: Whether to add spray points (points within hamming distance 1 of the center)
        sampler: The sampler to use for the acquisition function


    Returns:
        The candidate points, the function values at the candidate points, the new GP hyperparameters, and the new trust region state

    """

    # Get the indices of the continuous parameters
    # 从 AxUS 中提取出“连续变量”的所有列索引。
    # 这些变量不会在离散优化中被优化（它们会在 continuous 版本中优化）。
    indices_not_to_optimize = torch.tensor(
        [i for b, i in axus.bins_and_indices_of_type(ParameterType.CONTINUOUS)]
    )

    # Find the center of the trust region
    # 找出当前函数值 fx_scaled 最小的那个点（最优点）作为 TR 中心点；
    x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()

    # x_center should be in [0, 1]^d at this point
    # 然后复制 batch_size 份，作为每个 batch 任务的初始点。
    x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)

    # 如果用户提供了 x_bests（通常是之前 continuous 优化的结果），我们就“覆盖”掉中心点中连续变量那几列。
    if x_bests is not None:
        # replace
        x_centers[:, indices_not_to_optimize] = (
            x_bests[:, indices_not_to_optimize] + 1
        ) / 2

    # define the number of candidates as in the TuRBO paper
    # 设定候选点数量，受限于最大值 5000。这个策略来自 TuRBO 的经验公式：每维 200 个点。
    n_candidates = min(5000, max(2000, 200 * axus.target_dim))

    # 创建两个空张量，最终每个 batch 中用来存放：最佳候选点 x_best 及其采集函数值 fx_best（注意不是真实函数值，而是 -EI 值）
    x_batch_return = torch.zeros(
        (batch_size, axus.target_dim), dtype=x_scaled.dtype, device=x_scaled.device
    )
    fx_batch_return = torch.zeros(
        (batch_size, 1), dtype=fx_scaled.dtype, device=fx_scaled.device
    )

    # 对每个 batch_size 执行一次完整的候选点生成 + 局部搜索 ; 每轮最终会生成一个 x_best
    for batch_index in range(batch_size):

        # 如果传入的是单点采集函数（EI），直接用它；
        # 否则说明是批量采样，构建 qEI（batch Expected Improvement）；
        _acquisition_function = acquisition_function
        if acquisition_function is None:
            assert (
                sampler is not None
            ), "Either acquisition_function or sampler must be provided"

            # 取张量 x_batch_return 的所有列，并且只取前 batch_index 行。 表示前面已经采了哪些点（防止重复）
            x_pending = x_batch_return[:batch_index, :] if batch_index > 0 else None
            
            # 构建 qEI（batch Expected Improvement）采集函数
            """ 
                对于第一个点 (batch_index=0)：
                    X_pending=None
                    采集函数评估所有候选点，选择最佳点
                对于第二个点 (batch_index=1)：
                    X_pending 包含第一个选择的点
                    采集函数会考虑第一个点的存在，从而选择与第一个点互补的点
                对于第三个点 (batch_index=2)：
                    X_pending 包含前两个选择的点
                    采集函数会考虑前两个点的存在，从而选择与前两个点互补的点

            """
            _acquisition_function = qExpectedImprovement(
                model=model,
                best_f=(-fx_scaled).max().item(),
                sampler=sampler,
                X_pending=x_pending,
            )

        def ts(x: torch.Tensor, batch_index: int):
            """
            定义局部函数 ts()：给一个候选点 x，返回 负的采集函数值（越小越好）
            Get the approximate posterior sample of a specific batch index.

            Args:
                x: The points to evaluate the posterior sample at
                batch_index: The index of the batch to evaluate the posterior sample for

            Returns:
                The approximate posterior sample at the given points

            """

            return -_acquisition_function(x.unsqueeze(1))

        # 从当前信赖域中心出发，根据 trust_region.length_discrete 采样 n_candidates 个初始候选点
        x_candidates = sample_initial_points_discrete(
            x_center=x_centers[batch_index],
            axus=axus,
            tr_length=trust_region.length_discrete,
            n_initial_points=n_candidates,
        )

        # 添加额外扰动的“邻居点” —— 所有与中心点汉明距离为 1 的点
        if add_spray_points:
            x_spray = hamming_neighbors_within_tr(
                x_center=x_centers[batch_index],
                x=x_centers[batch_index],
                tr_length=trust_region.length_discrete,
                axus=axus,
            )
            # 按行拼接
            x_candidates = torch.vstack((x_candidates, x_spray))

        # Evaluate the acquisition function for all candidates
        # 对所有候选点评估采集函数值
        with torch.no_grad():
            candidate_acquisition_values = ts(x_candidates, batch_index=batch_index)

        # Find the top k candidates with the highest acquisition function value
        # 然后挑选 3 个采集函数最小（也就是 EI 最大）的点索引，准备做局部搜索
        # torch.topk(...) 返回的是一个包含两个张量的元组：（最大或最小的k个值，索引）[1]表示我们只需要拿到索引
        top_k_candidate_indices = torch.topk(
            candidate_acquisition_values,
            k=min(3, len(candidate_acquisition_values)),
            largest=False,
        )[1]

        # Start local search
        # 我们已经挑出了采集函数值最优的 top 3 候选点，现在对每一个进行汉明邻居搜索以进一步找更好的点。
        # 初始化
        best_posterior_value = torch.inf
        x_best = None

        # 对每个 top 候选点执行邻居搜索
        for top_index in top_k_candidate_indices:
            # x_candidate：当前待优化的候选点
            x_candidate = x_candidates[top_index, :].clone().unsqueeze(0)

            # posterior_value_k：该点的采集函数值
            posterior_value_k = candidate_acquisition_values[top_index].item()

            # 如果它比当前找到的最优还要好，就更新 x_best
            if posterior_value_k < best_posterior_value:
                best_posterior_value = posterior_value_k
                x_best = x_candidate


            while True:
                # 在当前 x_candidate 周围找所有符合 TR 限制的汉明邻居    
                x_start_neighbors = hamming_neighbors_within_tr(
                    x_center=x_centers[batch_index],
                    x=x_candidate,
                    tr_length=trust_region.length_discrete,
                    axus=axus,
                )

                # remove rows from x_start_neighbors that are already in self.x (which is a 2d tensor of shape (n, d))
                # 移除已经评估过的点 (x_scaled 实际上不包含所有历史采样点，而是当前信任域内的点)
                for x_eval in x_scaled.to(device=device):
                    x_start_neighbors = x_start_neighbors[
                        ~torch.all(x_start_neighbors == x_eval, dim=1)
                    ]

                # 如果删完邻居为空，跳出循环
                if x_start_neighbors.numel() == 0:
                    # no neighbors left, continue with next top candidate
                    break

                with torch.no_grad():
                    # 评估邻居点采集函数值
                    neighbors_acq_val = ts(x_start_neighbors, batch_index=batch_index)

                # 如果找到更优邻居
                if (
                    len(neighbors_acq_val) > 0
                    and torch.min(neighbors_acq_val) < posterior_value_k
                ):
                    x_candidate = x_start_neighbors[torch.argmin(neighbors_acq_val)]
                    posterior_value_k = torch.min(neighbors_acq_val).item()
                else:
                    # could not find a better neighbor, continue with next top candidate
                    break
                # 如果当前候选点最终得到的 posterior_value_k 更小，就更新全局 x_best
                if posterior_value_k < best_posterior_value:
                    best_posterior_value = posterior_value_k
                    x_best = x_candidate.unsqueeze(0)

        # 如果找不到更优点（所有邻居都不好）fallback 策略：直接用 TR 中心点作为这个 batch 的点
        if x_best is None:
            warnings.warn(
                "Could not find a better point than the center of the trust region"
            )
            logging.info( "Could not find a better point than the center of the trust region")
            # choose random point
            x_best = x_centers[batch_index].unsqueeze(0)

        # repeat x_cand batch_size many times
        # 把当前 batch 找到的最佳点 x_best 填入 x_batch_return 对应行；
        x_batch_return[batch_index, :] = x_best.squeeze()

        # 把当前 batch 找到的最佳点的采集函数值（注意：不是真实函数值）填入 fx_batch_return 对应行
        fx_batch_return[batch_index, :] = best_posterior_value

    # 确保算法没有错误地修改了不应该被优化的维度；在混合优化过程中，离散优化步骤只影响离散变量，不影响连续变量
    assert len(indices_not_to_optimize) == 0 or torch.any(
        x_centers[:, indices_not_to_optimize].squeeze()
        == x_batch_return[:, indices_not_to_optimize].squeeze()
    ), "x_ret should not be optimized at indices_not_to_optimize"

    # transform to [-1, 1], was [0, 1]
    # 将 [0, 1] 映射回 [-1, 1]
    x_batch_return = x_batch_return * 2 - 1

    # 保存当前信赖域中心点（未混合）；离散半径（整数）
    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_discrete]),
    }

    return x_batch_return, fx_batch_return, tr_state

# 这个函数的结构和离散版本类似，但专门用于优化 连续变量
def create_candidates_continuous(
    x_scaled: torch.Tensor,
    fx_scaled: torch.Tensor,
    acquisition_function: Optional[ExpectedImprovement],
    model: SingleTaskGP,
    axus: AxUS,
    trust_region: TrustRegion,
    device: str,
    batch_size: int,
    indices_to_optimize: Optional[torch.Tensor] = None,     # 表示哪些维度要优化（可用于混合变量中，仅优化 continuous）
    x_bests: Optional[list[torch.Tensor]] = None,
    sampler: Optional[SobolQMCNormalSampler] = None,
) -> tuple[torch.Tensor, torch.Tensor, dict]:
    """
    在当前信赖域内，使用采集函数 + 局部搜索，
    为连续变量生成 batch_size 个候选点 x_best，并返回它们的采集函数值和信赖域状态。
    Create candidate points for the next batch.

    Args:
        x_scaled: The current points in the trust region
        fx_scaled: The function values at the current points
        acquisition_function: The acquisition function to use
        model: The current GP model
        axus: The current AxUS embedding for the trust region
        trust_region: The current trust region state
        device: The device to use ('cpu' or 'cuda')
        indices_to_optimize: The indices of the candidate points to optimize (in case of mixed spaces)
        x_bests: The center of the trust region
        batch_size: int

    Returns:
        The candidate points, the function values at the candidate points, the new GP hyperparameters, and the new trust region state

    """

    # 如果没有显式告诉你“只优化某些维度”，那就默认所有维度都优化
    if indices_to_optimize is None:
        indices_to_optimize = torch.arange(axus.target_dim)

    # 找出剩下那些不需要优化的维度
    indices_not_to_optimize = torch.arange(axus.target_dim)[
        ~torch.isin(torch.arange(axus.target_dim), indices_to_optimize)
    ]

    # 找出当前函数值 fx_scaled 最小的那个点（最优点）作为 TR 中心点
    x_centers = torch.clone(x_scaled[fx_scaled.argmin(), :]).detach()
    # repeat x_centers batch_size many times
    # 为每个 batch 任务复制 batch_size 份，作为每个 batch 任务的初始点
    x_centers = torch.repeat_interleave(x_centers.unsqueeze(0), batch_size, dim=0)

    # 如果你从离散阶段已经得到了“固定部分”的最优解（例如分类变量已知），
    # 那就把之前得到的最优解的离散部分赋值，后续优化连续部分就好了
    if x_bests is not None:
        x_centers[:, indices_not_to_optimize] = (
            x_bests[:, indices_not_to_optimize] + 1
        ) / 2

    assert len(x_centers.shape) == 2, "x_center should be a 2d tensor"

    # 采集函数值的负值
    fx_argmins = torch.zeros(batch_size, dtype=torch.long, device=device)

    # 每个批次的最优候选点对应的采集函数值
    fx_mins = torch.zeros(batch_size, dtype=torch.double, device=device)

    # 每个批次的最优候选点（在低维嵌入空间中）
    x_cand_downs = torch.zeros(
        (batch_size, axus.target_dim), dtype=torch.double, device=device
    )

    for batch_index in range(batch_size):
        # 拿到当前批次的中心点
        x_center = x_centers[batch_index, :]

        # 提取当前 GP 模型的 lengthscale 参数
        if isinstance(model.covar_module.base_kernel, MixtureKernel):
            weights = model.covar_module.base_kernel.continuous_kernel.lengthscale.detach().squeeze(
                0
            )
        elif isinstance(model.covar_module.base_kernel, MaternKernel):
            weights = model.covar_module.base_kernel.lengthscale.detach().squeeze(0)
        else:
            raise NotImplementedError(
                "Only MixtureKernel and MaternKernel are supported"
            )
        
        # 标准化 lengthscale，变成相对权重
        # 先归一化为均值为 1
        weights /= weights.mean()

        # 再通过几何均值进一步缩放，使它们乘积为 1
        # torch.prod(...) 把所有开根后的数相乘，得到的是一个标量 —— 这个其实就是 几何平均值
        # 用当前的 weights 除以它的几何平均值，使得最终所有 weights 的乘积为 1
        # 这两步的作用能够得到一组均值为1，乘积也为1的权重。
        # 重要维度（小 lengthscale）：信任域较窄，搜索更精细
        # 不重要维度（大 lengthscale）：信任域较宽，搜索更粗略
        weights /= torch.prod(torch.pow(weights, 1 / len(weights)))

        # 从 x_center 中取出你要优化的维度（变量），构成一个新的子向量 _x_center。
        _x_center = x_center[indices_to_optimize]

        # torch.clip(..., 0, 1) → 确保下限不小于 0，不大于 1
        # trust_region.length_continuous * weights / 2 就是每个维度上的半宽
        _tr_lb = torch.clip(
            _x_center - trust_region.length_continuous * weights / 2, 0, 1
        )
        _tr_ub = torch.clip(
            _x_center + trust_region.length_continuous * weights / 2, 0, 1
        )


        tr_lb = torch.zeros(axus.target_dim, dtype=torch.double, device=device)
        tr_ub = torch.ones(axus.target_dim, dtype=torch.double, device=device)
        tr_lb[indices_to_optimize] = _tr_lb
        tr_ub[indices_to_optimize] = _tr_ub

    
        _acquisition_function = acquisition_function
        if acquisition_function is None:
            assert (
                sampler is not None
            ), "Either acquisition_function or sampler must be provided"
            x_pending = x_cand_downs[:batch_index, :] if batch_index > 0 else None
            _acquisition_function = qExpectedImprovement(
                model=model,
                best_f=(-fx_scaled).max().item(),
                sampler=sampler,
                X_pending=x_pending,
            )

        # EI-based acquisition function
        # 优化采集函数（重点）
        x_cand_down = optimize_acqf(
            acq_function=_acquisition_function,
            bounds=torch.stack([tr_lb, tr_ub], dim=0),
            q=1,
            fixed_features={            # 不参与优化的特征及其固定值
                i: x_center[i].item() for i in indices_not_to_optimize.tolist()
            },
            # 参数含义：当前随机512个点，然后找到ei最高的10个进行优化，最后再找到ei最高的1个点
            num_restarts=10,
            raw_samples=512,
        )
        x_cand_down, y_cand_down = x_cand_down
        x_cand_downs[batch_index, :] = x_cand_down
        fx_argmins[batch_index] = -y_cand_down

    # 构造 tr_state
    tr_state = {
        "center": x_scaled[fx_scaled.argmin(), :].detach().cpu().numpy().reshape(1, -1),
        "length": np.array([trust_region.length_continuous]),
        "lb": tr_lb.detach().cpu().numpy(),
        "ub": tr_ub.detach().cpu().numpy(),
    }

    return x_cand_downs * 2 - 1, fx_argmins.reshape(batch_size), tr_state


def sample_initial_points_discrete(
    x_center: torch.Tensor,
    tr_length: torch.Tensor,
    axus: AxUS,
    n_initial_points: int,
) -> torch.Tensor:
    """
    这是一个辅助函数，用于在离散搜索阶段随机采样一批候选点，作为后续采集函数打分的输入。

    Sample initial points for the discrete parameters

    Args:
        x_center: the center of the trust region
        tr_length: the length of the trust region
        axus: the AxUS embedding
        n_initial_points: the number of initial points to sample

    Returns:
        x_cand: the sampled initial points

    """

    # 获取所有离散参数类型（排除连续类型）
    discrete_parameter_types = [
        pt for pt in ParameterType if pt != ParameterType.CONTINUOUS
    ]

    # copy x_center n_initial_points times
    # # 复制中心点 n_initial_points 次；接下来会在这些复制的点上“有选择地修改某些维度”。
    x_cand = torch.repeat_interleave(x_center.unsqueeze(0), n_initial_points, dim=0)

    # 对不同类型的离散参数进行随机化
    for parameter_type in discrete_parameter_types:
        # 如果当前类型的bin数量为0，不需要优化
        if axus.n_bins_of_type(parameter_type) == 0:
            # No parameters of this type
            continue

        # 二进制参数
        if parameter_type == ParameterType.BINARY:
            # 拿到bin的索引
            indices = torch.tensor(
                [i for b, i in axus.bins_and_indices_of_type(parameter_type)]
            )
            # draw min(tr_length, len(indices)) indices for each candidate
            # 为每个候选点，随机选出 tr_length - 1 个 binary 维度
            indices_for_cand = torch.tensor(
                np.array(
                    [
                        np.random.choice(
                            indices, min(tr_length, len(indices)), replace=False
                        )
                        for _ in range(n_initial_points)
                    ]
                ),
                dtype=torch.long,
                device=x_cand.device,
            )
            # draw values for each index
            # 为这些位置采样新的值（0 或 1）
            values_for_cand = torch.randint(
                0,
                2,
                (n_initial_points, len(indices_for_cand[0])),
                dtype=x_cand.dtype,
                device=x_cand.device,
            )
            # set values for each candidate
            # 把这些新值写入对应的候选点位置
            x_cand = x_cand.scatter_(1, indices_for_cand, values_for_cand)
        elif parameter_type == ParameterType.CATEGORICAL:
            # 获取所有分类参数的索引组;例如 indicess = [[3,4], [7,8,9], [12,13]]，表示有 3 个分类变量；
            indicess = [i for b, i in axus.bins_and_indices_of_type(parameter_type)]

            # # 如果分类变量组数量超过信任域长度，随机选择部分 min(tr_length, len(indicess) 组
            if len(indicess) > tr_length:
                # index_setss的形式：[[1,2], [0,2], [1,2]]
                index_setss = [
                    np.random.choice(
                        np.arange(len(indicess)),
                        min(tr_length, len(indicess)),
                        replace=False,
                    )
                    # 选择n_initial_points次
                    for _ in range(n_initial_points)
                ]
                # 拿到索引以及对应索引组 例如i=1,index_sets=[0,2],这里的0,2又对应indicess中的[3,4],[12,13]
                for i, index_sets in enumerate(index_setss):
                    # 这里就是替换index_sets=[0,2]为[[3,4],[12,13]]
                    index_sets = [indicess[i] for i in index_sets]
                    # set x_cand to 0 for each index
                    # 涉及的one-hot变量全部变为0
                    x_cand[i, torch.cat(index_sets)] = 0

                    # 随机选择一位为1
                    if True:  # else:
                        # this is the expensive part
                        for indices in index_sets:
                            # set one index to 1
                            x_cand[i, np.random.choice(indices)] = 1
            else:
                # 处理和之前类似，所有涉及的fenlie
                for indices in indicess:
                    # set x_cand to 0 for each index
                    x_cand[:, indices] = 0
                    # sample n_initial_points indices
                    indices_for_cand = np.random.choice(indices, n_initial_points)
                    # set one index to 1
                    x_cand[torch.arange(n_initial_points), indices_for_cand] = 1
            pass

        elif parameter_type == ParameterType.ORDINAL:
            raise NotImplementedError("Ordinal parameters are not supported yet")
        else:
            raise ValueError(f"Unknown parameter type {parameter_type}")

    # remove duplicates
    # 移除重复点，移动到和最优点邻居合并之后
    # x_cand = torch.unique(x_cand, dim=0)

    # remove points that coincide with x_center
    # 移除与中心点相同的点
    x_cand = x_cand[torch.any(x_cand != x_center, dim=1), :]

    # remove candidates that are not within the trust region
    # 移除不在信任域内的点
    x_cand_in_tr = x_cand[hamming_distance(x_cand, x_center) <= tr_length, :]
    if len(x_cand_in_tr) == 0:
        logging.debug(f"No initial points in trust region, returning all candidates")
        
    # 如果信任域内有点，返回这些点；否则返回所有候选点，
    # 就是说如果点都不在TR内，可以适当放宽条件，保证算法运行
    return x_cand_in_tr if len(x_cand_in_tr) > 0 else x_cand


# 为了兼容现有的调用，创建一个别名函数
run_genetic_algorithm_discrete_ga = run_genetic_algorithm_discrete
